{"name": "@4-sure/ui-platform", "version": "0.11.0-alpha.1", "main": "./index.js", "types": "./index.d.ts", "repository": {"url": "git+https://github.com/4-sure/ui-platform.git"}, "publishConfig": {"registry": "https://npm.pkg.github.com"}, "peerDependencies": {"react-router-dom": "^6.24.0", "simplebar": "^6.2.7", "simplebar-react": "^3.2.6"}, "exports": {".": {"import": {"types": "./index.d.ts", "default": "./index.mjs"}, "require": {"types": "./index.d.ts", "default": "./index.js"}, "types": "./index.d.ts"}}, "dependencies": {"@4-sure/ui-platform": "file:"}}