import { useEffect, useRef, useState } from 'react';
import {
  Control,
  Controller,
  FieldError,
  RegisterOptions,
} from 'react-hook-form';
import styled, { css } from 'styled-components';
import { Icon, IconTypes } from '../../Icons';
import { TimePickerV2 } from './TimePickerV2';
import { TimePickerV2AlertModal } from './TimePickerV2AlertModal';

type Props = {
  name: string;
  placeholder?: string;
  icon?: IconTypes;
  iconPosition?: 'left' | 'right' | 'none';
  fieldError?: FieldError | undefined | null;
  rules?: RegisterOptions;
  control?: Control;
  label?: string;
  instructions?: string;
  timeString?: string;
  validMinutes?: number[];
  onFocus?: (ev: any) => void;
  onChange?: (value: string) => void;
  isafterhours?: any;
};

const Instruction = styled.div<{ error?: boolean }>`
  margin-top: ${(props) => props.theme.SpacingXs};
  color: ${({ error, theme }) =>
    error ? theme.ColorsUtilityColorError : theme.ColorsTypographySecondary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize2}px;
`;

const StyledInstuctionContainer = styled.div`
  margin: unset;
`;

const Wrapper = styled.div`
  display: grid;
  grid-auto-flow: row;
  gap: ${(props) => props.theme.SpacingXs};
  transition: color 1s ease, background-color 1s ease;
  position: relative;
  /* width: 100%; */
`;

const Container = styled.div`
  height: auto;
  min-height: 37px;
  display: grid;
  align-items: center;
  justify-content: center;
  min-width: 95px;
  cursor: pointer;
  grid-template-columns: 1fr;
  /* margin-top: ${(props) => props.theme.SpacingXs}; */

  &:last-of-type {
    border-right: none;
  }
`;

const ActiveZone = styled.div`
  display: grid;
  grid-template-rows: 1fr auto;
  gap: ${(props) => props.theme.SpacingSm};
  align-items: self-start;
  width: 100%;

  &.left {
    border-top-left-radius: 0.25rem;
    border-bottom-left-radius: 0.25rem;
  }
`;

const TimeInputContainer = styled.div<{
  dropdownOpen: boolean;
  error?: boolean;
}>`
  height: 37px;
  // overflow: hidden;
  z-index: 8;
  outline: unset;
  text-align: left;
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  background-color: ${(props) => props.theme.ColorsInputsPrimary};
  border: 1px solid
    ${(props) =>
      props.dropdownOpen
        ? props.theme.ColorsUtilityColorFocus
        : props.theme.ColorsTypographySecondary};
  color: #e5e5e5;
  background-color: #4a5055;
  border: 1px solid
    ${({ dropdownOpen, error }) =>
      dropdownOpen
        ? '#118ab2'
        : error
        ? css`
            ${(props) => props.theme.ColorsUtilityColorError}
          `
        : css`
            ${(props) => props.theme.ColorsStrokesGrey}
          `};
  box-sizing: border-box;
  width: 100%;
  padding: 8px;
  border-radius: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: grid;
  grid-template-areas: 'left-icon date-display right-icon dropdown-icon';
  grid-template-rows: 1fr;
  grid-template-columns: auto 1fr auto auto;
  align-content: center;
`;

const TimeInput = styled.input<{ dropdownOpen: boolean }>`
  all: unset;
  overflow: hidden;
  outline: unset;
  background-color: transparent;
  text-align: left;
  box-sizing: border-box;
  width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;

  ::placeholder {
    color: ${(props) =>
      props.dropdownOpen
        ? props.theme.ColorsUtilityColorFocus
        : props.theme.ColorsTypographySecondary};
    transition: color 0.3s ease;
  }
`;

const DropdownIcon = styled(Icon)`
  grid-area: dropdown-icon;
  padding: 0 ${(props) => props.theme.SpacingXs};
`;

const DropdownMenu = styled.div<{ width: number }>`
  margin: 0 !important;
  position: absolute;
  right: 0px;
  left: 0px;
  border-radius: 4px;
  background-color: #283133;
  border: 1px solid ${(props) => props.theme.ColorsUtilityColorFocus};
  box-sizing: border-box;
  display: grid;
  grid-template-rows: auto;

  color: ${(props) => props.theme.ColorsTypographyPrimary};
  padding-bottom: 5px;
  z-index: 1;
  width: 100%;

  div {
    color: #a5a5a5;
    outline: none;
    cursor: pointer;
    text-overflow: ellipsis;
  }
`;

const IconWrapper = styled(Icon)`
  display: grid;
  grid-auto-flow: column;
  place-items: center;
  align-self: center;
`;
const Label = styled.label`
  margin-bottom: ${(props) => props.theme.SpacingXs};
  color: ${(props) => props.theme.ColorsTypographyPrimary};
  font-family: ${(props) => props.theme.FontFamiliesInter};
  font-size: ${(props) => props.theme.FontSize3}px;
`;

const LeftIconContainer = styled.span<{ position: 'left' | 'right' | 'none' }>`
  grid-area: left-icon;
  padding: 0
    ${(props) => (props?.position === 'left' ? props.theme.SpacingXs : 0)};
`;
const RightIconContainer = styled.span<{ position: 'left' | 'right' | 'none' }>`
  grid-area: right-icon;
  padding: 0
    ${(props) => (props?.position === 'right' ? props.theme.SpacingXs : 0)};
`;

/**
 * A reusable time picker input component that allows users to select a time.
 *
 * @param {TimePickerV2InputProps} props - The component props.
 * @param {string} [props.placeholder] - The input placeholder text.
 * @param {string} [props.instructions] - The input instructions.
 * @param {string} [props.label] - The input label.
 * @param {'left' | 'right' | 'none'} [props.iconPosition] - The position of the icon.
 * @param {ReactNode} [props.icon] - The icon component.
 * @param {string} [props.name] - The input name.
 * @param {string} [props.timeString] - The initial time value.
 * @param {ControllerProps} props.control - The control props for the component.
 * @param {object} props.rules - The input rules.
 * @param {number[]} [props.validMinutes] - The valid minutes to select from.
 * @param {string} [props.fieldError] - The input field error.
 * @param {(event: React.FocusEvent<HTMLDivElement>) => void} [props.onFocus] - The on focus event handler.
 * @returns {ReactElement} The time picker input component.
 */
export const TimePickerV2Input = ({
  placeholder = '--:--',
  instructions,
  label,
  iconPosition: position = 'none',
  icon,
  name = 'time-picker',
  timeString,
  control,
  rules,
  validMinutes = [0, 15, 30, 45],
  fieldError,
  onFocus,
  onChange,
  isafterhours,
  ...rest
}: Props) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputContainerRef = useRef<HTMLDivElement>(null);
  const [afterHoursAlertVisible, setAfterHoursAlertVisible] = useState(false);

  useEffect(() => {
    /**
     * Updates the width of the dropdown menu to match the width of the input
     * container. This is necessary because the dropdown menu is absolutely
     * positioned and does not automatically resize to match the width of its
     * parent.
     */
    const updateDropdownWidth = () => {
      if (inputContainerRef.current) {
        const width = inputContainerRef.current.offsetWidth;
        if (dropdownRef.current) {
          dropdownRef.current.style.width = `${width}px`;
        }
      }
    };

    updateDropdownWidth();
    window.addEventListener('resize', updateDropdownWidth);

    /**
     * Handles the event when the user clicks outside of the dropdown
     * menu. If the click is outside of the dropdown menu, the dropdown
     * menu is closed by setting the dropdownOpen state to false.
     *
     * @param {MouseEvent} event - The mouse event that triggered the
     * click outside of the dropdown menu.
     */
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        inputContainerRef.current &&
        !inputContainerRef.current.contains(event.target as Node)
      ) {
        setDropdownOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('resize', updateDropdownWidth);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  /**
   * Toggles the dropdown menu open or closed when the input field is clicked.
   * @function
   * @param {MouseEvent} event - The click event.
   */
  const handleInputClick = () => {
    setDropdownOpen((prev) => !prev);
  };

  // useEffect(() => {
  //   if (isafterhours) {
  //     console.log('ISAFTERHOURRRRRRS', isafterhours);
  //     setAfterHoursAlertVisible(true);
  //   }
  // }, [isafterhours]);

  const handleTimeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTime = e.target.value;
    if (onChange) {
      onChange(newTime);
    }
  };

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({
        field: { value = timeString, name, onChange: controllerOnChange },
        fieldState: { error = fieldError },
      }) => (
        <Wrapper data-testid="time-picker-wrapper">
          {!!label && <Label>{label}</Label>}
          <Container data-testid="time-picker-container">
            <ActiveZone
              data-testid="time-picker-input-active-zone"
              className={'left'}
              ref={inputContainerRef}
            >
              <TimeInputContainer
                data-testid="time-picker-input-container"
                dropdownOpen={dropdownOpen}
                onClick={handleInputClick}
                error={!!error?.message}
              >
                <LeftIconContainer
                  data-testid="time-picker-input-left-icon"
                  position={position}
                >
                  {position === 'left' && !!icon && (
                    <IconWrapper type={icon} width={24} height={24} />
                  )}
                </LeftIconContainer>
                <TimeInput
                  dropdownOpen={dropdownOpen}
                  type="text"
                  placeholder={value || placeholder}
                  name={name}
                  readOnly
                  onChange={handleTimeChange}
                />
                <RightIconContainer position={position}>
                  {position === 'right' && !!icon && (
                    <IconWrapper type={icon} width={24} height={24} />
                  )}
                </RightIconContainer>
                <DropdownIcon
                  type={dropdownOpen ? 'chevron-up' : 'chevron-down'}
                />
              </TimeInputContainer>
              <div>
                {dropdownOpen && (
                  <DropdownMenu
                    data-testid="time-picker-dropdown"
                    width={inputContainerRef.current?.offsetWidth || 0}
                    ref={dropdownRef}
                  >
                    <TimePickerV2
                      // onSelect={(val: string) => onChange(val)}
                      onSelect={(val: string) => {
                        // Fire the controller's onChange to update the form
                        controllerOnChange(val);
                        // Also call the external onChange so that the GenerateControl gets its log
                        if (onChange) {
                          onChange(val);
                        }
                      }}
                      selected={value}
                      validMinutes={validMinutes}
                    />
                  </DropdownMenu>
                )}
              </div>
            </ActiveZone>
          </Container>
          {error ? (
            <Instruction error={!!error?.message}>{error?.message}</Instruction>
          ) : (
            <StyledInstuctionContainer>
              {instructions && <Instruction>{instructions}</Instruction>}
            </StyledInstuctionContainer>
          )}

          {afterHoursAlertVisible && (
            <TimePickerV2AlertModal
              closeModal={() => setAfterHoursAlertVisible(false)}
            />
          )}
        </Wrapper>
      )}
    />
  );
};
