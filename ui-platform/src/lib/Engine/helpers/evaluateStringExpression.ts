import { renderTemplateWithJS } from 'js-in-strings';
import { TemplateLiteralLogger } from '../../Utilities';
import { ActionConfig } from '../models';
import { getNestedProperty } from './client-utils';
import { parseAndExecuteQuery } from './query-utils';
import { QueryEngine, QueryResult } from './QueryEngine';
import { renderTemplate, renderTemplateObject } from './render-template';
import { templateFunctions } from './render-template-functions';

const dbg = new TemplateLiteralLogger({
  prefix: '👨‍💻[Evaluate String Expression]:',
  enabled: false,
  options: { style: { backgroundColor: '#DBF0D6', color: '#557942' } },
});

const err = TemplateLiteralLogger.createLog(
  {
    prefix: '❗[Evaluate String Expression]:',
    enabled: true,
    options: { style: { backgroundColor: '#FFE4DC', color: '#EB4B1A' } },
  },
  'error'
);

const warn = TemplateLiteralLogger.createLog({
  prefix: '⚠[Evaluate String Expression]:',
  enabled: true,
  options: { style: { backgroundColor: '#FFF4DC', color: '#F0A607' } },
});

const queryEngine = new QueryEngine();

export const evalStringExpression = <T extends Record<string, any>>(
  value: any,
  obj: T,
  options?: {
    debug?: boolean;
  }
) => {
  dbg.configure({
    prefix: '👨‍💻[Evaluate String Expression]:',
    enabled: options?.debug,
    options: { style: { backgroundColor: '#DBF0D6', color: '#557942' } },
  });
  const log = dbg.log;

  // Return non-string values as-is
  if (typeof value !== 'string') {
    return value;
  }

  let val;
  log`Evaluating... ${{ template: value, dataSrc: obj }}`;

  // Handle @param direct substitution
  if (value === '@param') {
    return obj;
  }

  // Handle @param template expressions
  if (value.startsWith('@param:{')) {
    const expression = value.substring(8, value.length - 1); // Remove '@param:{' and trailing '}'
    // If obj is undefined, return undefined
    if (obj === undefined || obj === null) {
      return undefined;
    }
    // Directly extract the value from obj using the path
    return getNestedProperty(obj, expression, undefined);
  }

  if (value.startsWith('$')) {
    // Existing store path logic
    const [storePath, query] = value.replace('$', '').split('?');
    log`Evaluating "$" templating processing ${storePath} ${obj} ${query}`;

    // Handle null/undefined store objects safely
    if (obj === null || obj === undefined) {
      val = '';
    } else {
      // Use undefined as default to distinguish from empty string
      val = getNestedProperty(obj, storePath, undefined);

      // If the value is explicitly null or undefined in the store, preserve it
      if (val === null || val === undefined) {
        // Check if the property actually exists in the object structure
        const pathParts = storePath.split('.');
        let current = obj;
        let exists = true;

        for (const part of pathParts) {
          if (current && typeof current === 'object' && part in current) {
            current = current[part];
          } else {
            exists = false;
            break;
          }
        }

        // If property exists but is null/undefined, keep that value
        // If property doesn't exist, return empty string
        val = exists ? current : '';
      }

      // Perform further query logic on array type value
      if (query && Array.isArray(val)) {
        try {
          val = parseAndExecuteQuery(val, query);
        } catch (error) {
          err`Error executing query for ${storePath}: ${error}`;
        }
      }
    }
  } else if (value.startsWith('#') && !value.includes('#{')) {
    // Handle simple # prefix templates (e.g., #user.name, #company) - ONLY when there's no #{} in the string
    log`Evaluating \"#\" templating processing ${value} ${obj}`;
    const templateWithoutPrefix = value.substring(1);

    let templateToProcess;
    if (
      !templateWithoutPrefix.includes(' ') &&
      !templateWithoutPrefix.includes('/') &&
      !templateWithoutPrefix.includes('{') &&
      !templateWithoutPrefix.includes('}') &&
      !templateWithoutPrefix.includes('#')
    ) {
      templateToProcess = `{${templateWithoutPrefix}}`;
    } else {
      templateToProcess = templateWithoutPrefix;
    }

    val = renderTemplate(
      templateToProcess,
      obj,
      templateFunctions(obj, obj?.formDataRaw)
    );

    // If template processing failed, return fallback
    if (val === templateToProcess) {
      if (
        templateWithoutPrefix.startsWith('{') &&
        templateWithoutPrefix.endsWith('}') &&
        templateWithoutPrefix.includes(' ')
      ) {
        val = value;
      } else if (
        templateToProcess.startsWith('{') &&
        templateToProcess.endsWith('}') &&
        !templateToProcess.includes(' ') &&
        !templateToProcess.includes('/')
      ) {
        val = templateToProcess.slice(1, -1);
      } else {
        val = templateWithoutPrefix;
      }
    }
  } else if (value.startsWith('q:')) {
    log`Evaluating "q:" templating processing ${value} ${obj}`;
    const query = value.replace('q:', '');
    const funcs = templateFunctions(obj, obj?.formDataRaw);
    const result = queryEngine.query(obj, query, {
      transformations: funcs as any,
      customFunctions: funcs as any,
    });
    try {
      if (Array.isArray(result)) {
        val = result.map((r) => r.value);
      } else {
        val = (result as QueryResult).value;
      }
    } catch (error) {
      err`Error executing query for ${query}: ${error}`;
      val = value;
    }
  } else if (value.startsWith('js:')) {
    // JavaScript template string processing with js: prefix
    let template = value.substring(3);
    log`Evaluating "js:" templating processing ${template} ${obj}`;
    if (template.startsWith('return ')) {
      const expression = template.substring(7);
      template = `{(() => { return ${expression}; })()}`;
    } else if (!template.startsWith('{')) {
      template = `{${template}}`;
    }

    const result = renderTemplateWithJS(template, obj, {
      contextExtensions: templateFunctions(obj, obj?.formDataRaw),
      returnRawValues: true,
      sandbox: true,
    });

    // Handling for error outputs
    if (typeof result === 'string' && result.startsWith('[Error:')) {
      err`Error evaluating js template: ${template} - ${result}`;
      val = value;
    } else {
      val = result;
    }
  } else if (/\{.*\}/.test(value)) {
    // Existing template string logic - if template contains curly brackets within ("{" and "}" respectively
    // and in that order). This also handles strings with #{} expressions
    log`Evaluating "{}" templating processing ${value} ${obj}`;
    const processedValue = value.replace(/#\{([^}]*)\}/g, '{$1}');

    val = renderTemplate(
      processedValue,
      obj,
      templateFunctions(obj, obj?.formDataRaw)
    );

    // If template processing failed and we had #{} expressions, return original value
    if (
      val === processedValue &&
      value.includes('#{') &&
      processedValue !== value
    ) {
      val = value;
    }
  } else {
    log`Evaluating "plain" string processing ${value} ${obj}`;
    val = value;
  }
  return val;
};

/**
 * Evaluates a condition expression against a context object.
 *
 * @param context - The object against which to evaluate the condition.
 * @param value - The condition expression to evaluate. May be a string (in which case it is
 *     passed to `evalStringExpression`) or a boolean (in which case it is returned directly).
 * @param options - An object with a single optional property `debug` which, if true, will
 *     cause debug logging to be emitted.
 * @returns - The result of evaluating the condition expression.
 */
export const evalConditionExpression = (
  context: any,
  value?: string | boolean,
  options?: {
    debug?: boolean;
  }
) => {
  let result = false;
  const val =
    typeof value === 'string'
      ? evalStringExpression(value, context, options)
      : value;

  if (typeof val === 'boolean') {
    result = val;
  }
  return result;
};

export const isSpreadOperator = (key: string, value: any): boolean => {
  // Support both $ and $spread keys
  return (
    (key === '$' || key === '$spread') &&
    typeof value === 'string' &&
    value.startsWith('...{') &&
    value.endsWith('}')
  );
};

export const processTemplates = <S, T extends Record<string, any>>(
  obj: S,
  storeObj: T,
  options?: {
    debug?: boolean;
    excludeKeys?: string[];
  }
): any => {
  dbg.configure({
    prefix: '👨‍💻[Process Templates]:',
    enabled: options?.debug,
    options: { style: { backgroundColor: '#cff1c7', color: '#3e0b5eff' } },
  });
  const log = dbg.log;
  log`Processing template... ${{ template: obj, dataSrc: storeObj }}`;
  if (typeof obj === 'string') {
    log`Process templates evaluating string ${obj} ${storeObj}`;
    return evalStringExpression(obj, storeObj);
  }
  if (Array.isArray(obj)) {
    log`Process templates evaluating array through recursion and iteration ${obj} ${storeObj}`;
    return obj.map((item) => processTemplates(item, storeObj, options));
  }
  if (obj && typeof obj === 'object') {
    log`Process templates evaluating object ${obj} ${storeObj}`;
    if (
      Object.keys(obj).includes('$') ||
      Object.keys(obj).includes('$spread')
    ) {
      // if (isSpreadOperator(k, v)) {
      // Extract the path from the spread syntax
      log`Process templates evaluating spread object using renderTemplateObject ${obj} ${storeObj}`;
      const spreadRes = renderTemplateObject(
        obj,
        storeObj,
        templateFunctions(storeObj, storeObj?.formDataRaw)
      );
      return spreadRes;
      // }
    }
    log`Process templates evaluating object through iteration amd recursion ${obj} ${storeObj}`;
    const result: any = {};
    for (const [k, v] of Object.entries(obj)) {
      log`Process templates evaluating object key ${k}:${v} with source - ${storeObj}`;
      if (options?.excludeKeys?.includes(k)) {
        // skip excluded keys from recursive template processing
        result[k] = v;
        continue;
      } else {
        result[k] = processTemplates(v, storeObj, options);
      }
    }
    log`Object template processing result ${result}`;
    return result;
  }
  return obj;
};

interface ParseResult {
  groups: string[];
  baseUrl: string;
  expressions: Array<{
    type: 'hash' | 'javascript' | 'variable' | 'query';
    content: string;
    fullMatch: string;
  }>;
}

export function parseTemplateUrl(url: string): ParseResult {
  const groups: string[] = [];
  const expressions: ParseResult['expressions'] = [];

  const baseUrlMatch = url.match(/^(\{[^}]+\}[^#$]*?)(?=#|js:|[?&][^=]*=\$|$)/);
  const baseUrl = baseUrlMatch ? baseUrlMatch[1] : '';

  const patterns = {
    hash: /#(?:\{([^}]*)\}|([^#${}?&]*?))(?=#|\$|\?|&|$)/g, // #{...} or #... until next expression or end
    javascript: /js:(\{[^}]*\})/g, // js:{...}
    variable: /\$([a-zA-Z_][a-zA-Z0-9_.]*)/g, // $variable or $object.property
    queryParam: /([&?][^=]+=)(?=\$)/g, // Query parameter names before $
  };

  let lastIndex = 0;

  while (lastIndex < url.length) {
    const currentLastIndex = lastIndex;
    let earliestMatch: {
      match: RegExpExecArray;
      type: keyof typeof patterns;
    } | null = null;
    let earliestIndex = url.length;

    Object.entries(patterns).forEach(([type, pattern]) => {
      pattern.lastIndex = currentLastIndex; // Use the local copy of lastIndex
      const match = pattern.exec(url);
      if (match && match.index < earliestIndex) {
        earliestMatch = { match, type: type as keyof typeof patterns };
        earliestIndex = match.index;
      }
    });

    if (!earliestMatch) break;

    type TMatch = {
      match: RegExpExecArray;
      type: keyof typeof patterns;
    };

    if (
      earliestMatch &&
      (earliestMatch as TMatch)?.match &&
      typeof (earliestMatch as TMatch).match.index === 'number'
    ) {
      const { match, type } = earliestMatch;

      if ((match as RegExpExecArray).index > lastIndex) {
        const beforeContent = url.substring(
          lastIndex,
          (match as RegExpExecArray).index
        );
        if (beforeContent.trim()) {
          groups.push(beforeContent);
        }
      }

      switch (type) {
        case 'hash': {
          // Handle both #{...} and #... formats - match[1] is from #{...}, match[2] is from #...
          const hashContent = match[1] || match[2];
          groups.push('#' + hashContent);
          expressions.push({
            type: 'hash',
            content: hashContent,
            fullMatch: match[0],
          });
          break;
        }

        case 'javascript':
          groups.push('js:' + match[1]);
          expressions.push({
            type: 'javascript',
            content: match[1],
            fullMatch: match[0],
          });
          break;

        case 'variable':
          groups.push('$' + match[1]);
          expressions.push({
            type: 'variable',
            content: match[1],
            fullMatch: match[0],
          });
          break;

        case 'queryParam':
          groups.push(match[1]);
          expressions.push({
            type: 'query',
            content: match[1],
            fullMatch: match[0],
          });
          break;
      }

      lastIndex =
        match && (match as RegExpExecArray).index + (match[0] as string).length;
    } else {
      break;
    }
  }

  if (lastIndex < url.length) {
    const remaining = url.substring(lastIndex);
    if (remaining.trim()) {
      groups.push(remaining);
    }
  }

  return {
    groups,
    baseUrl,
    expressions,
  };
}

export const simpleProcessUrlTemplateString = (
  baseUrl: string,
  storeObj: Record<string, any> = {},
  debug = false
) => {
  dbg.configure({
    prefix: '👨‍💻[Process Url Template String]:',
    enabled: debug,
    options: { style: { backgroundColor: '#DBF0D6', color: '#557942' } },
  });
  dbg.log`Parameters to be processed ${{ baseUrl, storeObj }}`;
  let parsedUrl = parseTemplateUrl(baseUrl);
  parsedUrl = processTemplates(parsedUrl, storeObj);
  dbg.log`Url template string output ${{
    parsedUrl,
    outputURL: parsedUrl.groups.join(''),
  }}`;
  return parsedUrl.groups.join('');
};

export interface ProcessUrlTemplateOptions {
  /** Whether to URL encode the processed values */
  encodeValues?: boolean;
  /** Whether to preserve empty query parameters */
  preserveEmptyParams?: boolean;
  /** Whether to process template expressions in the base URL (protocol + domain) */
  processBaseUrl?: boolean;
  /** Custom base URL boundary pattern (defaults to first path separator after protocol) */
  baseUrlBoundary?: RegExp;
  /** Custom error handler for template processing errors */
  onError?: ((error: Error, expression: string) => string) | ActionConfig[];
  /** Debug option (false by default) */
  debug?: boolean;
  /** Client action function for executing function calls from configs */
  callClientAction?: (config: ActionConfig[]) => void;
}

/**
 * Processes a URL template string by parsing template expressions and generating a complete URL
 *
 * @param urlTemplate - The URL template string containing expressions
 * @param storeObj - The data object for template evaluation
 * @param options - Optional configuration for URL processing
 * @returns The fully processed URL string
 *
 * @example
 * ```typescript
 * const template = '{apiBase}/users/#{userId}?status=$filter&limit=js:{Math.min(limit, 100)}';
 * const data = {
 *   apiBase: 'https://api.example.com',
 *   userId: 123,
 *   filter: 'active',
 *   limit: 50
 * };
 *
 * const url = processUrlTemplateString(template, data);
 * // Result: 'https://api.example.com/users/123?status=active&limit=50'
 *
 * // Skip processing base URL
 * const urlWithBaseProtected = processUrlTemplateString(template, data, {
 *   processBaseUrl: false
 * });
 * // Base URL expressions like {apiBase} won't be processed
 * ```
 */
export function processUrlTemplateString<T extends Record<string, any>>(
  urlTemplate: string,
  storeObj: T,
  options: ProcessUrlTemplateOptions = {}
): string {
  const {
    encodeValues = false,
    preserveEmptyParams = false,
    processBaseUrl = true,
    debug = false,
    baseUrlBoundary,
    onError,
    callClientAction,
  } = options;

  dbg.configure({
    prefix: '👨‍💻[Process Url Template String]:',
    enabled: debug,
    options: { style: { backgroundColor: '#DBF0D6', color: '#557942' } },
  });

  dbg.log`Parameters to be processed ${{ urlTemplate, storeObj }}`;

  try {
    const urlParts = processBaseUrl
      ? { baseUrl: '', remainingUrl: urlTemplate }
      : splitUrlAtBase(urlTemplate, baseUrlBoundary);
    dbg.log`URL Parts ${{ urlParts }}`;
    const parsed = parseTemplateUrl(urlParts.remainingUrl);

    dbg.log`Url template string output ${{
      parsedUrl: parsed,
      outputURL: parsed.groups.join(''),
    }}`;

    let processedUrl = '';

    if (!processBaseUrl) {
      processedUrl = urlParts.baseUrl;
      dbg.log`Processed url ${{
        processedUrl,
      }}`;
    }

    const processedExpressions = new Set<string>();

    for (const group of parsed.groups) {
      try {
        if (isTemplateExpression(group)) {
          if (processedExpressions.has(group)) {
            processedUrl += group;
            continue;
          }

          processedExpressions.add(group);

          const processedValue = evalStringExpression(group, storeObj, {
            debug: options.debug,
          });

          let finalValue = processedValue;
          dbg.log`Final value ${{
            group,
            finalValue,
          }}`;

          if (finalValue !== null && finalValue !== undefined) {
            finalValue = String(finalValue);

            if (encodeValues && shouldEncodeValue(group, finalValue)) {
              finalValue = encodeURIComponent(finalValue);
            }
          } else {
            finalValue = '';
          }

          if (
            isQueryParameter(group) &&
            finalValue === '' &&
            !preserveEmptyParams
          ) {
            continue;
          }

          processedUrl += finalValue;
        } else {
          processedUrl += group;
        }
        dbg.log`Processed URL ${{ processedUrl }}`;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : String(error);
        warn`Error processing URL template expression "${group}": ${errorMessage}`;

        if (onError) {
          if (
            Array.isArray(onError) &&
            callClientAction &&
            (onError satisfies ActionConfig[])
          ) {
            callClientAction(onError);
            processedUrl += '';
          } else {
            const fallbackValue = (
              onError as (e: Error, val: string) => string
            )(error instanceof Error ? error : new Error(errorMessage), group);
            processedUrl += fallbackValue;
          }
        } else {
          processedUrl += group;
        }
      }
    }

    const outputUrl = cleanupUrl(processedUrl);
    dbg.log`Url template string output ${{
      outputURL: outputUrl,
    }}`;
    return outputUrl;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    err`Error processing URL template "${urlTemplate}": ${errorMessage}`;

    if (onError) {
      if (
        Array.isArray(onError) &&
        callClientAction &&
        (onError satisfies ActionConfig[])
      ) {
        callClientAction(onError);
        return '';
      }
      return typeof onError === 'function'
        ? onError(
            error instanceof Error ? error : new Error(errorMessage),
            urlTemplate
          )
        : '';
    }

    return urlTemplate;
  }
}

/**
 * Splits a URL at the base URL boundary (protocol + domain)
 * @param url - The URL to split
 * @param customBoundary - Custom regex pattern for the boundary
 * @returns Object with baseUrl and remainingUrl parts
 */
function splitUrlAtBase(
  url: string,
  customBoundary?: RegExp
): { baseUrl: string; remainingUrl: string } {
  const defaultBoundary = /^(https?:\/\/[^/]+)/i;
  const boundary = customBoundary || defaultBoundary;

  const match = url.match(boundary);

  if (match) {
    const baseUrl = match[1];
    const remainingUrl = url.substring(baseUrl.length);
    return { baseUrl, remainingUrl };
  }

  const protocolMatch = url.match(/^([^:]+:\/\/[^/?#]+)/);
  if (protocolMatch) {
    const baseUrl = protocolMatch[1];
    const remainingUrl = url.substring(baseUrl.length);
    return { baseUrl, remainingUrl };
  }

  const templateMatch = url.match(/^([{][^}]+[}])/);
  if (templateMatch) {
    return { baseUrl: '', remainingUrl: url };
  }

  const slashIndex = url.indexOf('/');
  if (slashIndex > 0) {
    return {
      baseUrl: url.substring(0, slashIndex),
      remainingUrl: url.substring(slashIndex),
    };
  }

  return { baseUrl: '', remainingUrl: url };
}

/**
 * Determines if a string is a template expression
 * @param str - string to check if it is string template
 * @returns boolean
 */
function isTemplateExpression(str: string): boolean {
  return (
    str.startsWith('#') ||
    str.startsWith('$') ||
    str.startsWith('js:') ||
    /\{.*\}/.test(str) ||
    str.startsWith('q:')
    // (str.startsWith('{') && str.endsWith('}'))
  );
}

/**
 * Determines if a group represents a query parameter
 * @param group - string to check if it is structured like a query param
 * @returns boolean
 */
function isQueryParameter(group: string): boolean {
  return group.includes('?') || group.includes('&');
}

/**
 * Determines if a value should be URL encoded based on its context
 * @param originalExpression - string expression
 * @param value - value to be evaluated if url encoded
 * @return boolean
 */
function shouldEncodeValue(originalExpression: string, value: string): boolean {
  if (value.startsWith('http://') || value.startsWith('https://')) {
    return false;
  }

  if (originalExpression.startsWith('#') && value.includes('%')) {
    return false;
  }

  if (isQueryParameter(originalExpression)) {
    return true;
  }

  return /[^a-zA-Z0-9\-._~]/.test(value);
}

/**
 * Cleans up the processed URL by removing duplicate separators and formatting
 * @param url - url string
 * @return string
 */
function cleanupUrl(url: string): string {
  return url
    .replace(/([^:]\/)\/+/g, '$1')
    .replace(/\?&/g, '?')
    .replace(/&&+/g, '&')
    .replace(/\?$/g, '')
    .replace(/&$/g, '');
}

/**
 * Advanced URL template processor with additional features
 *
 * @param urlTemplate - The URL template string
 * @param storeObj - The data object for template evaluation
 * @param options - Advanced processing options
 * @returns ProcessedUrlResult with URL and metadata
 *
 * @example
 * ```typescript
 * const result = processUrlTemplateAdvanced(
 *   '{apiBase}/users/{userId}?fields=#{fields.join(",")}&active=$userActive',
 *   {
 *     apiBase: 'https://api.example.com',
 *     userId: 123,
 *     fields: ['name', 'email'],
 *     userActive: true
 *   },
 *   {
 *     validateUrl: true,
 *     includeMetadata: true,
 *     processBaseUrl: false  // Skip processing base URL templates
 *   }
 * );
 *
 * console.log(result.url); // Processed URL
 * console.log(result.metadata.expressionsProcessed); // Number of expressions processed
 * ```
 */
export interface ProcessedUrlResult {
  url: string;
  metadata: {
    originalTemplate: string;
    expressionsProcessed: number;
    expressionsFound: number;
    processingTime: number;
    baseUrlProtected: boolean;
    baseUrlPart?: string;
    errors: Array<{ expression: string; error: string }>;
  };
}

/**
 * Processes an advanced URL template string, incorporating template expressions,
 * error handling, and optional validation.
 *
 * @param urlTemplate - The URL template string containing expressions to be processed.
 * @param storeObj - The data object used for evaluating expressions within the template.
 * @param options - Additional options for processing the URL template:
 *   - validateUrl (optional): If true, validate the final URL format.
 *   - includeMetadata (optional): If true, include metadata about processing.
 *   - Other options inherited from ProcessUrlTemplateOptions.
 * @returns An object containing the processed URL and metadata about the URL processing.
 *
 * @example
 * ```typescript
 * const result = processUrlTemplateAdvanced(
 *   '{apiBase}/users/{userId}?fields=#{fields.join(",")}&active=$userActive',
 *   {
 *     apiBase: 'https://api.example.com',
 *     userId: 123,
 *     fields: ['name', 'email'],
 *     userActive: true
 *   },
 *   { validateUrl: true, includeMetadata: true }
 * );
 *
 * console.log(result.url); // Processed URL
 * console.log(result.metadata.expressionsProcessed); // Number of expressions processed
 * ```
 */

export function processUrlTemplateAdvanced<T extends Record<string, any>>(
  urlTemplate: string,
  storeObj: T,
  options: ProcessUrlTemplateOptions & {
    validateUrl?: boolean;
    includeMetadata?: boolean;
  } = {}
): ProcessedUrlResult {
  dbg.configure({
    prefix: '👨‍💻[Process Url Template String Advanced]:',
    enabled: options.debug,
    options: { style: { backgroundColor: '#DBF0D6', color: '#557942' } },
  });

  dbg.log`Parameters to be processed ${{ urlTemplate, storeObj }}`;

  const startTime = Date.now();
  const errors: Array<{ expression: string; error: string }> = [];

  const errorHandler = (error: Error, expression: string) => {
    errors.push({ expression, error: error.message });
    if (
      options.onError &&
      Array.isArray(options.onError) &&
      (options.onError satisfies ActionConfig[]) &&
      options.callClientAction
    ) {
      options?.callClientAction(options.onError satisfies ActionConfig[]);
      return expression;
    }
    return options.onError
      ? (options.onError as (err: Error, exp: string) => string)(
          error,
          expression
        )
      : expression;
  };

  const urlParts =
    options.processBaseUrl !== false
      ? { baseUrl: '', remainingUrl: urlTemplate }
      : splitUrlAtBase(urlTemplate, options.baseUrlBoundary);

  const templateToParse = urlParts.remainingUrl;
  const parsed = parseTemplateUrl(templateToParse);

  const processedUrl = processUrlTemplateString(urlTemplate, storeObj, {
    ...options,
    onError: errorHandler,
  });

  if (options.validateUrl) {
    try {
      new URL(processedUrl);
    } catch (urlError) {
      errors.push({
        expression: 'final_url',
        error: `Invalid URL format: ${
          urlError instanceof Error ? urlError.message : String(urlError)
        }`,
      });
      err`Invalid URL format: ${processedUrl} - ${
        errors[errors.length - 1].error
      }`;
    }
  }

  const result: ProcessedUrlResult = {
    url: processedUrl,
    metadata: {
      originalTemplate: urlTemplate,
      expressionsProcessed: parsed.expressions.length,
      expressionsFound: parsed.expressions.length,
      processingTime: Date.now() - startTime,
      baseUrlProtected: options.processBaseUrl === false,
      baseUrlPart:
        options.processBaseUrl === false ? urlParts.baseUrl : undefined,
      errors,
    },
  };

  return result;
}

/**
 * Recursively checks if the given object or any of its nested properties/values
 * contain template strings (starting with '#', '$', 'q:', 'js:' or {expr}).
 * Safe against circular references using a WeakSet.
 */
export const hasTemplatesRecursive = (
  obj: any,
  visited: WeakSet<object> = new WeakSet()
): boolean => {
  if (typeof obj === 'string') {
    return (
      obj.startsWith('#') ||
      obj.startsWith('$') ||
      obj.startsWith('q:') ||
      obj.startsWith('js:') ||
      !!obj.match(/\{.*\}/)
    );
  }
  if (Array.isArray(obj)) {
    if (visited.has(obj as any)) return false;
    visited.add(obj as any);
    const res = obj.some((item) => hasTemplatesRecursive(item, visited));
    visited.delete(obj as any);
    return res;
  }
  if (obj && typeof obj === 'object') {
    if (visited.has(obj)) return false;
    visited.add(obj);
    const res = Object.values(obj).some((v) =>
      hasTemplatesRecursive(v as any, visited)
    );
    visited.delete(obj);
    return res;
  }
  return false;
};
