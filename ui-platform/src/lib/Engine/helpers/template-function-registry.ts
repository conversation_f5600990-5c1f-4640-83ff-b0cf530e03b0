/*
 * Template Function Registry
 * - Type-safe registration of template functions (supports namespacing via dot notation)
 * - Override control and conflict detection
 * - Single global registry instance + registration helpers
 */

export type TemplateFunc<Args extends any[] = any[], Ret = any> = (
  ...args: Args
) => Ret;

export type TemplateFunctionMap = Record<string, TemplateFunc>;

export interface RegisterOptions {
  /** Allow replacing an existing function of the same fully-qualified name */
  override?: boolean;
  /** Optional namespace to prepend (e.g., "myApp" => myApp.calculateTax) */
  namespace?: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface CustomTemplateFunctions extends TemplateFunctionMap {}

/**
 * A global, namespaced function registry for template evaluation.
 * Keys are fully-qualified function names (supports dot notation for namespaces).
 */
export class TemplateFunctionRegistry {
  private functions: Map<string, TemplateFunc> = new Map();

  /**
   * Register multiple functions at once.
   * @param funcs Map of functionName -> function implementation. Can be any arity.
   * @param options Optional registration options: namespace and override flag.
   * @example
   * // Registers myApp.calculateTax and myApp.formatCurrency
   * registry.register({ calculateTax: (amt: number, rate: number) => amt * rate, formatCurrency: (n: number) => `${n}` }, { namespace: 'myApp' });
   */
  register<T extends TemplateFunctionMap>(
    funcs: T,
    options: RegisterOptions = {}
  ): void {
    const ns = options.namespace?.trim();

    Object.entries(funcs).forEach(([name, fn]) => {
      const fullName = ns ? `${ns}.${name}` : name;
      const exists = this.functions.has(fullName);
      if (exists && !options.override) {
        throw new Error(
          `Template function conflict: "${fullName}" is already registered. Use { override: true } to replace it.`
        );
      }
      this.functions.set(fullName, fn);
    });
  }

  /**
   * Register a single named function.
   * @param name Function name without namespace.
   * @param fn Function implementation.
   * @param options Use { namespace: 'ns', override: true } to place under a namespace or replace existing.
   * @example
   * registry.registerFn('calc', (a: number,b:number)=>a+b, { namespace: 'myApp' }); // myApp.calc
   */
  registerFn<Args extends any[], Ret>(
    name: string,
    fn: TemplateFunc<Args, Ret>,
    options: Omit<RegisterOptions, 'namespace'> & { namespace?: string } = {}
  ) {
    const fullName = options.namespace?.trim()
      ? `${options.namespace}.${name}`
      : name;
    const exists = this.functions.has(fullName);
    if (exists && !options.override) {
      throw new Error(
        `Template function conflict: "${fullName}" is already registered. Use { override: true } to replace it.`
      );
    }
    this.functions.set(fullName, fn);
  }

  /**
   * Returns a plain object copy of all registered functions.
   * Use this object to provide functions to template engines (#{}, js:, q:).
   */
  getAll(): TemplateFunctionMap {
    const out: TemplateFunctionMap = {};
    for (const [k, v] of this.functions.entries()) out[k] = v;
    return out;
  }

  /** Clear all registered functions (useful for test isolation). */
  clear() {
    this.functions.clear();
  }
}

// Global singleton
export const templateFunctionRegistry = new TemplateFunctionRegistry();

/**
 * Convenience API for app-level registration.
 * @param funcs Functions map; generics preserve arg/return types for IntelliSense at the call site.
 * @param options Optional { namespace, override }.
 * @example
 * registerTemplateFunctions({ calculateSPCommission: (a:number,r:number)=>a*r }, { namespace: 'fieldOps', override: true });
 * // usage in template: '#{fieldOps.calculateSPCommission(amount, 0.15)}'
 */
export function registerTemplateFunctions<T extends TemplateFunctionMap>(
  funcs: T,
  options?: RegisterOptions
) {
  templateFunctionRegistry.register(funcs, options);
}
