import React from 'react';
import { InteractionBlockerProps } from '../../Components';
// import { componentMap } from "../../component-map";

export interface FragmentConfig {
  // component: keyof typeof componentMap;
  component: string;
  props: { [key: string]: any };
  layout: React.CSSProperties;
  isStickyHeader?: boolean;
  disableComponentConfig?: Omit<InteractionBlockerProps, 'disableCondition'> & {
    disableCondition?: string | boolean;
    noIconLabel?: boolean;
  };
  debounceMs?: number;
  debug?: boolean;
  // Store keys that should be truthy/loaded before mounting
  requireStoreKeys?: string[];
}
