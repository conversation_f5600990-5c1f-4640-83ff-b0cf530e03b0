import { act, cleanup, render, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { useAppStore } from '../../useAppStore';
import { useAsyncLoaderStore } from '../useAsyncLoaderStore';
import { useDataReadiness } from '../useDataReadiness';

// Mock react-router-dom useNavigation to avoid requiring a Data Router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>(
    'react-router-dom'
  );
  return { ...actual, useNavigation: () => ({ state: 'idle' }) } as any;
});

function Probe(props: any) {
  const { ready, routeReady, loadersReady, storeReady, requiredKeys } =
    useDataReadiness(props.options);
  return (
    <div>
      <div data-testid="ready">{String(ready)}</div>
      <div data-testid="routeReady">{String(routeReady)}</div>
      <div data-testid="loadersReady">{String(loadersReady)}</div>
      <div data-testid="storeReady">{String(storeReady)}</div>
      <div data-testid="keys">{(requiredKeys || []).join(',')}</div>
    </div>
  );
}

describe('useDataReadiness', () => {
  beforeEach(() => {
    cleanup();
    // reset stores
    act(() => {
      useAsyncLoaderStore.setState({ asyncLoading: false });
      useAppStore.getState().reset();
    });
  });

  afterEach(() => cleanup());

  it('gates component by route, loader and store keys when listed', () => {
    // Configure: gate component "CompA" and require store keys
    const options = {
      componentName: 'CompA',
      componentsRequiringDataLoad: ['CompA'],
      requiredStoreKeys: ['filtersData', 'auth'],
      autoDetectEnabled: false,
    };

    // Initial store state (from default): filtersData: [], auth: {}
    // loaders idle and route idle (mocked)
    render(<Probe options={options} />);

    expect(screen.getByTestId('routeReady').textContent).toBe('true');
    expect(screen.getByTestId('loadersReady').textContent).toBe('true');
    // store not ready because defaults are empty structures
    expect(screen.getByTestId('storeReady').textContent).toBe('false');
    expect(screen.getByTestId('ready').textContent).toBe('false');

    // Satisfy store readiness
    act(() => {
      useAppStore
        .getState()
        .setState((s) => ({ filtersData: [1], auth: { user: 1 } }));
    });

    // Re-render by unmounting and mounting fresh to avoid duplicate testids in DOM
    cleanup();
    render(<Probe options={options} />);
    expect(screen.getByTestId('storeReady').textContent).toBe('true');
    expect(screen.getByTestId('ready').textContent).toBe('true');
  });

  it('ignores gating when component is not listed', () => {
    const options = {
      componentName: 'CompB',
      componentsRequiringDataLoad: ['CompA'],
      requiredStoreKeys: ['filtersData'],
      autoDetectEnabled: false,
    };

    render(<Probe options={options} />);
    expect(screen.getByTestId('ready').textContent).toBe('true');
  });

  it('respects asyncLoading flag from useAsyncLoaderStore', () => {
    const options = {
      componentName: 'CompA',
      componentsRequiringDataLoad: ['CompA'],
      requiredStoreKeys: [],
      autoDetectEnabled: false,
    };

    // Simulate loaders in-flight
    act(() => {
      useAsyncLoaderStore.getState().setAsyncLoading(true);
    });
    render(<Probe options={options} />);
    expect(screen.getByTestId('loadersReady').textContent).toBe('false');
    expect(screen.getByTestId('ready').textContent).toBe('false');

    // Mark loaders as done
    act(() => {
      useAsyncLoaderStore.getState().setAsyncLoading(false);
    });
    // Clean and re-render to avoid duplicate nodes
    cleanup();
    render(<Probe options={options} />);
    expect(screen.getByTestId('loadersReady').textContent).toBe('true');
  });
});
