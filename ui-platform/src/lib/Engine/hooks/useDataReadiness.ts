import { useEffect, useMemo, useState } from 'react';
import { useNavigation } from 'react-router-dom';
import { analyzeRequiredStoreKeys } from '../helpers/store-key-analysis';
import { useAppStore } from '../useAppStore';
import { useAsyncLoaderStore } from './useAsyncLoaderStore';

export interface DataReadinessOptions {
  componentsRequiringDataLoad?: string[];
  componentName?: string;
  // Store keys that should be truthy/loaded before mounting
  requiredStoreKeys?: string[];
  // Optional: automatically detect store keys from component config props
  autoDetectFromProps?: Record<string, unknown> | null;
  autoDetectEnabled?: boolean;
}

/**
 * Aggregates multiple readiness signals to avoid mounting heavy components
 * before necessary data is available.
 */
export function useDataReadiness(options: DataReadinessOptions = {}) {
  const {
    componentName,
    componentsRequiringDataLoad = [],
    requiredStoreKeys = [],
    autoDetectFromProps = null,
    autoDetectEnabled = true,
  } = options;

  const navigation = useNavigation();
  const asyncLoading = useAsyncLoaderStore((s: any) => s.asyncLoading);

  // Derive final required keys
  const derivedKeys = useMemo(() => {
    if (!autoDetectEnabled || !autoDetectFromProps) return requiredStoreKeys;
    try {
      const detected = Array.from(
        analyzeRequiredStoreKeys(autoDetectFromProps)
      );
      const merged = Array.from(
        new Set([...(requiredStoreKeys || []), ...detected])
      );
      return merged;
    } catch (e) {
      return requiredStoreKeys;
    }
  }, [autoDetectEnabled, autoDetectFromProps, requiredStoreKeys]);

  // Store-level readiness: verify required keys exist and are non-empty if arrays
  const store = useAppStore((state) => state);
  const storeReady = useMemo(() => {
    if (!derivedKeys?.length) return true;
    return derivedKeys.every((key) => {
      const val = (store as any)?.[key];
      if (Array.isArray(val)) return val.length > 0;
      if (val && typeof val === 'object') return Object.keys(val).length > 0;
      return !!val;
    });
  }, [store, derivedKeys]);

  // Route-level readiness: react-router navigation idle
  const routeReady = navigation.state === 'idle';

  // State/screen-level readiness: leverage async loader store used by actions & fetch calls
  const loadersReady = asyncLoading === false;

  // Component-specific need: only gate if the component is listed
  const shouldGate = componentsRequiringDataLoad.includes(componentName || '');

  const ready = shouldGate ? routeReady && loadersReady && storeReady : true;

  // Small stabilization: once ready, maintain ready until unmount
  const [latchedReady, setLatchedReady] = useState<boolean>(ready);
  useEffect(() => {
    if (ready) setLatchedReady(true);
  }, [ready]);

  return {
    ready: latchedReady,
    routeReady,
    loadersReady,
    storeReady,
    requiredKeys: derivedKeys,
  };
}
