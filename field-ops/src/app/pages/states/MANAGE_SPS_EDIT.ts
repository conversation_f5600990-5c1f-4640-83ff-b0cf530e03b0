import {
  ActionConfig,
  bbbeee,
  StateConfig,
  TextConfig,
  validationRegex,
} from '@4-sure/ui-platform';
import { of } from 'ramda';

/*
 * SECTION: SPS/EDIT STATE
 * SPS edit state section where sps can be reviewed in detail and actioned
 */
// #region SPS/EDIT STATE
export const MANAGE_SPS_EDIT_STATE = {
  title: { template: 'manage-sp-edit' },
  fetchCalls: [
    // #region SPS EDIT STATE FETCHCALLS
    // fetchcalls that will be made the state level
    {
      key: 'sp_enums',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_enum',
      body: { enum: 'all' },
      slicePath: 'payload',
    },
    {
      key: 'sp_history',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp_history',
      body: { sp_id: '$object_id' },
      slicePath: 'payload',
    },
    {
      key: 'sp_profile',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
      body: { sp_id: '$object_id' },
      slicePath: 'payload',
    },
    {
      key: 'company_documentation',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/file_actions/get_files',
      body: {
        with_thumbnails: true,
        sp_id: '$object_id',
        order: '-',
      },
      slicePath: 'payload',
    },
  ],
  // #endregion
  defaultScreen: 'status',
  screens: {
    // #region SPS/EDIT/STATUS SCREEN
    status: {
      layout: {},
      fetchCalls: [],
      onEnter: [
        {
          type: 'clientAction',
          action: 'log',
          payload: [
            'THIS IS A TEST IN ALL CAPS!!!',
            '#:{fieldOps.testAddFunc(1,1)}',
            'ONBOARDING STATUS DROPDOWN OPTIONS',
            '#:{fieldOps.getAvailableOnboardingStateOptions(sp_profile.onboarding_state)}',
            'ONBOARDING STATUS',
            '#:{fieldOps.getOnboardingState(sp_profile.onboarding_state)}',
            'CERTIFICATION DOCUMENTS',
            'js:{fieldOps.getSPCertificationDocuments(company_documentation, sp_enums)}',
            'SP CLIENTS',
            'js:{fieldOps.getSPClients(sp_profile, sp_enums)}',
          ],
        },
      ],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Status',
                path: '../status',
              },
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'ProfileHero',
          layout: {
            display: 'grid',
            justifyContent: 'center',
          },
          props: {
            fullname: '$sp_profile.details.name',
            subText: 'Registration number: ',
            username: '$sp_profile.details.co_reg',
            active: false,
            image: '$sp_profile.company_profile_picture',
            profileType: 'company',
            state: '$sp_profile.details.onboarding_state',
            showImgUpload: false,
          },
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Services',
                options: {
                  format: 'heading',
                  type: 'section-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            // width: 'calc(100% - 226px - 56px)',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              platform_status: '$sp_profile.onboarding_state',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'single-select',
                  name: 'platform_status',
                  label: 'Connexa platform status',
                  instructions: ['Which is the current status of the company'],
                  valueProp: 'id',
                  labelProp: 'name',
                  options: {
                    source: 'literal',
                    data: `js:{const data = [
                      {
                        id: 6,
                        name: 'Disable',
                      },
                      {
                        id: 11,
                        name: 'Suspend',
                      },
                      {
                        id: 10,
                        name: 'Move to Training',
                      },
                      {
                        id: 3,
                        name: 'Activate',
                      },
                    ]
                    return data.filter(state => (sp_profile?.onboarding_state || 3) !== state.id)}`,
                  },
                  notSearchable: true,
                  placeholder: `js:{
                      const onboarding_states = {
                        3:'ACTIVE',
                        6: 'DISABLED',
                        11: 'SUSPENDED',
                        10: 'TRAINING'
                      };
                      return onboarding_states[sp_profile.onboarding_state]
                    }`,
                  onDropdownSelectChange: [
                    {
                      type: 'clientAction',
                      action: 'switch',
                      payload: {
                        pathToValue: 'formDataRaw.platform_status',
                        cases: [
                          {
                            caseName: 3,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    heading: 'Caution',
                                    headingType: 'page-heading',
                                    layout: {
                                      display: 'grid',
                                      gridAutoFlow: 'row',
                                      rowGap: '2rem',
                                      columnGap: '1rem',
                                      justifyItems: 'center',
                                      alignContent: 'space-around',
                                      height: 'auto',
                                      paddingTop: '2rem',
                                      paddingBottom: '2rem',
                                      width: '100%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    onClose: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName:
                                                'platform_status_change_reason',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName:
                                                'platform_status_change_notes',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName: 'platform_status',
                                              defaultValue:
                                                '#{sp_profile.onboarding_state}',
                                            },
                                            {
                                              fieldName: 'suspension_duration',
                                              defaultValue: undefined,
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'clearStore',
                                        payload: ['platform_status_selected'],
                                      },
                                    ],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Activating this SP will restore their ability to receive pings and they will become active on the system.',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                            {
                                              text: 'Are you sure you want to enable this SP?',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                          ],
                                        },
                                        layout: {
                                          justifyItems: 'center',
                                          display: 'grid',
                                          gap: '1rem',
                                          textAlign: 'center',
                                          maxWidth: '712px',
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: 'auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'No, cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          'platform_status_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status_change_notes',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status',
                                                        defaultValue:
                                                          '#{sp_profile.onboarding_state}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'suspension_duration',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'platform_status_selected',
                                                  ],
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Yes, continue',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'triggerModal',
                                                  payload: [
                                                    {
                                                      display: true,
                                                      type: 'warning',
                                                      heading: 'Activate SP',
                                                      headingType:
                                                        'page-heading',
                                                      onClose: [
                                                        {
                                                          type: 'clientAction',
                                                          action: 'resetFields',
                                                          payload: {
                                                            fields: [
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_reason',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_notes',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status',
                                                                defaultValue:
                                                                  '#{sp_profile.onboarding_state}',
                                                              },
                                                              {
                                                                fieldName:
                                                                  'suspension_duration',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                            ],
                                                          },
                                                        },
                                                        {
                                                          type: 'clientAction',
                                                          action: 'clearStore',
                                                          payload: [
                                                            'platform_status_selected',
                                                          ],
                                                        },
                                                      ],
                                                      layout: {},
                                                      onEnter: [],
                                                      onLeave: [],
                                                      fragments: [
                                                        {
                                                          component:
                                                            'FormBuilder',
                                                          props: {
                                                            defaultValues: {
                                                              platform_status_change_reason:
                                                                '',
                                                              platform_status_change_notes:
                                                                '',
                                                            },
                                                            config: {
                                                              style: {
                                                                display: 'grid',
                                                                gridAutoFlow:
                                                                  'row',
                                                                rowGap: '2rem',
                                                                columnGap:
                                                                  '1rem',
                                                                justifyItems:
                                                                  'center',
                                                                alignContent:
                                                                  'space-around',
                                                                height: 'auto',
                                                                paddingTop:
                                                                  '2rem',
                                                                paddingBottom:
                                                                  '2rem',
                                                                width: '100%',
                                                              },
                                                              controls: [
                                                                {
                                                                  type: 'single-select',
                                                                  name: 'platform_status_change_reason',
                                                                  label:
                                                                    'Reason for activating sp',
                                                                  placeholder:
                                                                    'Select a Reason',
                                                                  labelProp:
                                                                    'name',
                                                                  valueProp:
                                                                    'id',
                                                                  options: {
                                                                    source:
                                                                      'literal',
                                                                    data: 'js:{sp_enums.reasons_onboarding.filter(i => i.onboarding_id === 3)}',
                                                                  },
                                                                  notSearchable:
                                                                    true,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                                {
                                                                  type: 'textarea',
                                                                  name: 'platform_status_change_notes',
                                                                  label:
                                                                    'Notes',
                                                                  rows: 10,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          layout: {
                                                            justifyItems:
                                                              'center',
                                                            display: 'grid',
                                                          },
                                                        },
                                                        {
                                                          component:
                                                            'ButtonRow',
                                                          layout: {
                                                            width:
                                                              'fit-content',
                                                            margin: 'auto',
                                                          },
                                                          props: {
                                                            buttons: [
                                                              {
                                                                btnValue:
                                                                  'Cancel activating SP',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status',
                                                                          defaultValue:
                                                                            '#{sp_profile.onboarding_state}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'suspension_duration',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'clearStore',
                                                                    payload: [
                                                                      'platform_status_selected',
                                                                    ],
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                  },
                                                                ],
                                                              },
                                                              {
                                                                btnValue:
                                                                  'Activate SP',
                                                                disabledWhen:
                                                                  '!$store.formDataRaw.platform_status_change_reason',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'triggerFetchCall',
                                                                    payload: [
                                                                      {
                                                                        key: 'sp_profile',
                                                                        method:
                                                                          'POST',
                                                                        url: '{VITE_SP_SERVER}/api/v1/spaas_actions/force_onboarding_state',
                                                                        body: {
                                                                          onboarding_state_id: 3,
                                                                          sp_id:
                                                                            '{sp_profile.id}',
                                                                          reason_id:
                                                                            '{formDataRaw.platform_status_change_reason}',
                                                                          detailed_reason:
                                                                            '{formDataRaw.platform_status_change_notes|""}',
                                                                        },
                                                                        slicePath:
                                                                          'payload',
                                                                      },
                                                                    ],
                                                                    asyncLoadStart:
                                                                      true,
                                                                    async: true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                    asyncLoadEnd:
                                                                      true,
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        },
                                                      ],
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                  },
                                ],
                                debounce: 5,
                              },
                            ],
                          },
                          {
                            caseName: 6,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    heading: 'Caution',
                                    headingType: 'page-heading',
                                    layout: {
                                      display: 'grid',
                                      gridAutoFlow: 'row',
                                      rowGap: '2rem',
                                      columnGap: '1rem',
                                      justifyItems: 'center',
                                      alignContent: 'space-around',
                                      height: 'auto',
                                      paddingTop: '2rem',
                                      paddingBottom: '2rem',
                                      width: '100%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    onClose: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName:
                                                'platform_status_change_reason',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName:
                                                'platform_status_change_notes',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName: 'platform_status',
                                              defaultValue:
                                                '#{sp_profile.onboarding_state}',
                                            },
                                            {
                                              fieldName: 'suspension_duration',
                                              defaultValue: undefined,
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'clearStore',
                                        payload: ['platform_status_selected'],
                                      },
                                    ],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Disabling this SP will remove their ability to receive pings and they will be removed from the system.',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                            {
                                              text: 'Are you sure you want to disable this SP?',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                          ],
                                        },
                                        layout: {
                                          justifyItems: 'center',
                                          display: 'grid',
                                          gap: '1rem',
                                          textAlign: 'center',
                                          maxWidth: '712px',
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: 'auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'No, cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          'platform_status_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status_change_notes',
                                                        defaultValue: undefined,
                                                      },

                                                      {
                                                        fieldName:
                                                          'platform_status',
                                                        defaultValue:
                                                          '#{sp_profile.onboarding_state}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'suspension_duration',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'platform_status_selected',
                                                  ],
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Yes, continue',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'triggerModal',
                                                  payload: [
                                                    {
                                                      display: true,
                                                      type: 'warning',
                                                      heading: 'Disable SP',
                                                      headingType:
                                                        'page-heading',
                                                      layout: {},
                                                      onEnter: [],
                                                      onLeave: [],
                                                      onClose: [
                                                        {
                                                          type: 'clientAction',
                                                          action: 'resetFields',
                                                          payload: {
                                                            fields: [
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_reason',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_notes',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status',
                                                                defaultValue:
                                                                  '#{sp_profile.onboarding_state}',
                                                              },
                                                              {
                                                                fieldName:
                                                                  'suspension_duration',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                            ],
                                                          },
                                                        },
                                                        {
                                                          type: 'clientAction',
                                                          action: 'clearStore',
                                                          payload: [
                                                            'platform_status_selected',
                                                          ],
                                                        },
                                                      ],
                                                      fragments: [
                                                        {
                                                          component:
                                                            'FormBuilder',
                                                          props: {
                                                            defaultValues: {
                                                              platform_status_change_reason:
                                                                '',
                                                              platform_status_change_notes:
                                                                '',
                                                            },
                                                            config: {
                                                              style: {
                                                                display: 'grid',
                                                                gridAutoFlow:
                                                                  'row',
                                                                rowGap: '2rem',
                                                                columnGap:
                                                                  '1rem',
                                                                justifyItems:
                                                                  'center',
                                                                alignContent:
                                                                  'space-around',
                                                                height: 'auto',
                                                                paddingTop:
                                                                  '2rem',
                                                                paddingBottom:
                                                                  '2rem',
                                                                width: '100%',
                                                              },
                                                              controls: [
                                                                {
                                                                  type: 'single-select',
                                                                  name: 'platform_status_change_reason',
                                                                  label:
                                                                    'Reason for disabling sp',
                                                                  placeholder:
                                                                    'Select a Reason',
                                                                  labelProp:
                                                                    'name',
                                                                  valueProp:
                                                                    'id',
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'Please provide reason for disabling SP.',
                                                                    },
                                                                  },
                                                                  options: {
                                                                    source:
                                                                      'literal',
                                                                    data: 'js:{sp_enums.reasons_onboarding.filter(i => i.onboarding_id === 6)}',
                                                                  },
                                                                  notSearchable:
                                                                    true,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                                {
                                                                  type: 'textarea',
                                                                  name: 'platform_status_change_notes',
                                                                  label:
                                                                    'Notes',
                                                                  rows: 10,
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'This field is required',
                                                                    },
                                                                  },
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          layout: {
                                                            justifyItems:
                                                              'center',
                                                            display: 'grid',
                                                          },
                                                        },
                                                        {
                                                          component:
                                                            'ButtonRow',
                                                          layout: {
                                                            width:
                                                              'fit-content',
                                                            margin: 'auto',
                                                          },
                                                          props: {
                                                            buttons: [
                                                              {
                                                                btnValue:
                                                                  'Cancel disabling SP',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status',
                                                                          defaultValue:
                                                                            '#{sp_profile.onboarding_state}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'suspension_duration',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'clearStore',
                                                                    payload: [
                                                                      'platform_status_selected',
                                                                    ],
                                                                    async: true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                  },
                                                                ],
                                                              },
                                                              {
                                                                btnValue:
                                                                  'Disable SP',
                                                                disabledWhen:
                                                                  '!$store.formDataRaw.platform_status_change_reason',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'triggerFetchCall',
                                                                    payload: [
                                                                      {
                                                                        key: 'sp_profile',
                                                                        method:
                                                                          'POST',
                                                                        url: '{VITE_SP_SERVER}/api/v1/spaas_actions/force_onboarding_state',
                                                                        body: {
                                                                          onboarding_state_id: 6,
                                                                          sp_id:
                                                                            '{sp_profile.id}',
                                                                          reason_id:
                                                                            '{formDataRaw.platform_status_change_reason}',
                                                                          detailed_reason:
                                                                            '{formDataRaw.platform_status_change_notes}',
                                                                        },
                                                                        slicePath:
                                                                          'payload',
                                                                      },
                                                                    ],
                                                                    async: true,
                                                                    asyncLoadStart:
                                                                      true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                    asyncLoadEnd:
                                                                      true,
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        },
                                                      ],
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                  },
                                ],
                                debounce: 5,
                              },
                            ],
                          },
                          {
                            caseName: 10,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    heading: 'Caution',
                                    headingType: 'page-heading',
                                    layout: {
                                      display: 'grid',
                                      gridAutoFlow: 'row',
                                      rowGap: '2rem',
                                      columnGap: '1rem',
                                      justifyItems: 'center',
                                      alignContent: 'space-around',
                                      height: 'auto',
                                      paddingTop: '2rem',
                                      paddingBottom: '2rem',
                                      width: '100%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    onClose: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName:
                                                'platform_status_change_reason',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName:
                                                'platform_status_change_notes',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName: 'platform_status',
                                              defaultValue:
                                                '#{sp_profile.onboarding_state}',
                                            },
                                            {
                                              fieldName: 'suspension_duration',
                                              defaultValue: undefined,
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'clearStore',
                                        payload: ['platform_status_selected'],
                                      },
                                    ],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Moving this SP to training will remove their ability to receive pings, if they had been previously actiove. Until they complete their re-training, they will not receive new jobs although they will still have access to the SP Management and their workflow.',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                            {
                                              text: 'Are you sure you want to move SP to training?',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                },
                                              },
                                            },
                                          ],
                                        },
                                        layout: {
                                          justifyItems: 'center',
                                          display: 'grid',
                                          gap: '1rem',
                                          textAlign: 'center',
                                          maxWidth: '712px',
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: 'auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'No, cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          'platform_status_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status_change_notes',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status',
                                                        defaultValue:
                                                          '#{sp_profile.onboarding_state}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'suspension_duration',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'platform_status_selected',
                                                  ],
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Yes, continue',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'triggerModal',
                                                  payload: [
                                                    {
                                                      display: true,
                                                      type: 'warning',
                                                      heading:
                                                        'Move SP to Training',
                                                      headingType:
                                                        'page-heading',
                                                      layout: {},
                                                      onEnter: [],
                                                      onLeave: [],
                                                      onClose: [
                                                        {
                                                          type: 'clientAction',
                                                          action: 'resetFields',
                                                          payload: {
                                                            fields: [
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_reason',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_notes',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status',
                                                                defaultValue:
                                                                  '#{sp_profile.onboarding_state}',
                                                              },
                                                              {
                                                                fieldName:
                                                                  'suspension_duration',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                            ],
                                                          },
                                                        },
                                                        {
                                                          type: 'clientAction',
                                                          action: 'clearStore',
                                                          payload: [
                                                            'platform_status_selected',
                                                          ],
                                                        },
                                                      ],
                                                      fragments: [
                                                        {
                                                          component:
                                                            'FormBuilder',
                                                          props: {
                                                            defaultValues: {
                                                              platform_status_change_reason:
                                                                '',
                                                              platform_status_change_notes:
                                                                '',
                                                            },
                                                            config: {
                                                              style: {
                                                                display: 'grid',
                                                                gridAutoFlow:
                                                                  'row',
                                                                rowGap: '2rem',
                                                                columnGap:
                                                                  '1rem',
                                                                justifyItems:
                                                                  'center',
                                                                alignContent:
                                                                  'space-around',
                                                                height: 'auto',
                                                                paddingTop:
                                                                  '2rem',
                                                                paddingBottom:
                                                                  '2rem',
                                                                width: '100%',
                                                              },
                                                              controls: [
                                                                {
                                                                  type: 'single-select',
                                                                  name: 'platform_status_change_reason',
                                                                  label:
                                                                    'Reason for moving SP to training',
                                                                  placeholder:
                                                                    'Select a Reason',
                                                                  labelProp:
                                                                    'name',
                                                                  valueProp:
                                                                    'id',
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'Please provide reason for moving SP to training.',
                                                                    },
                                                                  },
                                                                  options: {
                                                                    source:
                                                                      'literal',
                                                                    data: 'js:{sp_enums.reasons_onboarding.filter(i => i.onboarding_id === 10)}',
                                                                  },
                                                                  notSearchable:
                                                                    true,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                                {
                                                                  type: 'textarea',
                                                                  name: 'platform_status_change_notes',
                                                                  label:
                                                                    'Notes',
                                                                  rows: 10,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          layout: {
                                                            justifyItems:
                                                              'center',
                                                            display: 'grid',
                                                          },
                                                        },
                                                        {
                                                          component:
                                                            'ButtonRow',
                                                          layout: {
                                                            width:
                                                              'fit-content',
                                                            margin: 'auto',
                                                          },
                                                          props: {
                                                            buttons: [
                                                              {
                                                                btnValue:
                                                                  'Cancel Training',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status',
                                                                          defaultValue:
                                                                            '#{sp_profile.onboarding_state}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'suspension_duration',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'clearStore',
                                                                    payload: [
                                                                      'platform_status_selected',
                                                                    ],
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                  },
                                                                ],
                                                              },
                                                              {
                                                                btnValue:
                                                                  'Move SP to Training',
                                                                disabledWhen:
                                                                  '!$store.formDataRaw.platform_status_change_reason',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'triggerFetchCall',
                                                                    payload: [
                                                                      {
                                                                        key: 'sp_profile',
                                                                        method:
                                                                          'POST',
                                                                        url: '{VITE_SP_SERVER}/api/v1/spaas_actions/force_onboarding_state',
                                                                        body: {
                                                                          onboarding_state_id: 10,
                                                                          sp_id:
                                                                            '{sp_profile.id}',
                                                                          reason_id:
                                                                            '{formDataRaw.platform_status_change_reason}',
                                                                          detailed_reason:
                                                                            '{formDataRaw.platform_status_change_notes|""}',
                                                                        },
                                                                        slicePath:
                                                                          'payload',
                                                                      },
                                                                    ],
                                                                    async: true,
                                                                    asyncLoadStart:
                                                                      true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                    asyncLoadEnd:
                                                                      true,
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        },
                                                      ],
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                  },
                                ],
                                debounce: 5,
                              },
                            ],
                          },
                          {
                            caseName: 11,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'triggerModal',
                                payload: [
                                  {
                                    display: true,
                                    type: 'warning',
                                    heading: 'Caution',
                                    headingType: 'page-heading',
                                    layout: {
                                      display: 'grid',
                                      gridAutoFlow: 'row',
                                      rowGap: '2rem',
                                      columnGap: '1rem',
                                      justifyItems: 'center',
                                      alignContent: 'space-around',
                                      height: 'auto',
                                      paddingTop: '2rem',
                                      paddingBottom: '2rem',
                                      width: '100%',
                                    },
                                    onEnter: [],
                                    onLeave: [],
                                    onClose: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName:
                                                'platform_status_change_reason',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName:
                                                'platform_status_change_notes',
                                              defaultValue: undefined,
                                            },
                                            {
                                              fieldName: 'platform_status',
                                              defaultValue:
                                                '#{sp_profile.onboarding_state}',
                                            },
                                            {
                                              fieldName: 'suspension_duration',
                                              defaultValue: undefined,
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'clearStore',
                                        payload: ['platform_status_selected'],
                                      },
                                    ],
                                    fragments: [
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Suspending this SP will remove their ability to receive pings and they will not get any new jobs for the duration of the suspension.',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                  maxWidth: '712px',
                                                  textAlign: 'center',
                                                },
                                              },
                                            },
                                          ],
                                        },
                                        layout: {
                                          justifyItems: 'center',
                                          display: 'grid',
                                          textAlign: 'center',
                                          maxWidth: '712px',
                                        },
                                      },
                                      {
                                        component: 'Text',
                                        props: {
                                          textItems: [
                                            {
                                              text: 'Are you sure you want to suspend this SP?',
                                              options: {
                                                format: 'heading',
                                                type: 'sub-heading',
                                                layout: {
                                                  paddingTop: '2rem',
                                                  maxWidth: '712px',
                                                  textAlign: 'center',
                                                },
                                              },
                                            },
                                          ],
                                        },
                                        layout: {
                                          justifyItems: 'center',
                                          display: 'grid',
                                          textAlign: 'center',
                                          maxWidth: '712px',
                                        },
                                      },
                                      {
                                        component: 'ButtonRow',
                                        layout: {
                                          width: 'fit-content',
                                          margin: 'auto',
                                        },
                                        props: {
                                          buttons: [
                                            {
                                              btnValue: 'No, cancel',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          'platform_status_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status_change_notes',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'platform_status',
                                                        defaultValue:
                                                          '#{sp_profile.onboarding_state}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'suspension_duration',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'platform_status_selected',
                                                  ],
                                                },
                                              ],
                                            },
                                            {
                                              btnValue: 'Yes, continue',
                                              onClick: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'closeModal',
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'triggerModal',
                                                  payload: [
                                                    {
                                                      display: true,
                                                      type: 'warning',
                                                      heading: 'Suspend SP',
                                                      headingType:
                                                        'page-heading',
                                                      layout: {},
                                                      onEnter: [],
                                                      onLeave: [],
                                                      onClose: [
                                                        {
                                                          type: 'clientAction',
                                                          action: 'resetFields',
                                                          payload: {
                                                            fields: [
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_reason',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status_change_notes',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                              {
                                                                fieldName:
                                                                  'platform_status',
                                                                defaultValue:
                                                                  '#{sp_profile.onboarding_state}',
                                                              },
                                                              {
                                                                fieldName:
                                                                  'suspension_duration',
                                                                defaultValue:
                                                                  undefined,
                                                              },
                                                            ],
                                                          },
                                                        },
                                                        {
                                                          type: 'clientAction',
                                                          action: 'clearStore',
                                                          payload: [
                                                            'platform_status_selected',
                                                          ],
                                                        },
                                                      ],
                                                      fragments: [
                                                        {
                                                          component:
                                                            'FormBuilder',
                                                          props: {
                                                            defaultValues: {
                                                              platform_status_change_reason:
                                                                '',
                                                              platform_status_change_notes:
                                                                '',
                                                              suspension_duration:
                                                                '',
                                                            },
                                                            config: {
                                                              style: {
                                                                display: 'grid',
                                                                gridAutoFlow:
                                                                  'row',
                                                                rowGap: '2rem',
                                                                columnGap:
                                                                  '1rem',
                                                                justifyItems:
                                                                  'center',
                                                                alignContent:
                                                                  'space-around',
                                                                height: 'auto',
                                                                paddingTop:
                                                                  '2rem',
                                                                paddingBottom:
                                                                  '2rem',
                                                                width: '100%',
                                                              },
                                                              controls: [
                                                                {
                                                                  type: 'single-select',
                                                                  name: 'suspension_duration',
                                                                  label:
                                                                    'Length of suspension',
                                                                  placeholder:
                                                                    'Select a time frame',
                                                                  labelProp:
                                                                    'name',
                                                                  valueProp:
                                                                    'days',
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'Please provide reason for disabling SP.',
                                                                    },
                                                                  },
                                                                  options: {
                                                                    source:
                                                                      'store',
                                                                    storeDataPath:
                                                                      'sp_enums.suspend_periods',
                                                                  },
                                                                  notSearchable:
                                                                    true,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                                {
                                                                  type: 'single-select',
                                                                  name: 'platform_status_change_reason',
                                                                  label:
                                                                    'Reason for suspension',
                                                                  placeholder:
                                                                    'Select a Reason',
                                                                  labelProp:
                                                                    'name',
                                                                  valueProp:
                                                                    'id',
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'Please provide reason for suspending SP.',
                                                                    },
                                                                  },
                                                                  options: {
                                                                    source:
                                                                      'literal',
                                                                    data: 'js:{sp_enums.reasons_onboarding.filter(i => i.onboarding_id === 11)}',
                                                                  },
                                                                  notSearchable:
                                                                    true,
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                                {
                                                                  type: 'textarea',
                                                                  name: 'platform_status_change_notes',
                                                                  label:
                                                                    'Notes',
                                                                  rows: 10,
                                                                  validation: {
                                                                    required: {
                                                                      value:
                                                                        true,
                                                                      message:
                                                                        'This field is required',
                                                                    },
                                                                  },
                                                                  css: {
                                                                    wrapper: {
                                                                      width:
                                                                        '100%',
                                                                    },
                                                                  },
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          layout: {
                                                            justifyItems:
                                                              'center',
                                                            display: 'grid',
                                                          },
                                                        },
                                                        {
                                                          component:
                                                            'ButtonRow',
                                                          layout: {
                                                            width:
                                                              'fit-content',
                                                            margin: 'auto',
                                                          },
                                                          props: {
                                                            buttons: [
                                                              {
                                                                btnValue:
                                                                  'Cancel suspension',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'platform_status_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },

                                                                        {
                                                                          fieldName:
                                                                            'platform_status',
                                                                          defaultValue:
                                                                            '#{sp_profile.onboarding_state}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'suspension_duration',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                    async: true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'clearStore',
                                                                    payload: [
                                                                      'platform_status_selected',
                                                                    ],
                                                                    async: true,
                                                                  },
                                                                ],
                                                              },
                                                              {
                                                                btnValue:
                                                                  'Suspend SP',
                                                                disabledWhen:
                                                                  '!$store.formDataRaw.platform_status_change_reason',
                                                                onClick: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'triggerFetchCall',
                                                                    payload: [
                                                                      {
                                                                        key: 'sp_profile',
                                                                        method:
                                                                          'POST',
                                                                        url: '{VITE_SP_SERVER}/api/v1/spaas_actions/force_onboarding_state',
                                                                        body: {
                                                                          onboarding_state_id: 11,
                                                                          sp_id:
                                                                            '{sp_profile.id}',
                                                                          reason_id:
                                                                            '{formDataRaw.platform_status_change_reason}',
                                                                          detailed_reason:
                                                                            '{formDataRaw.platform_status_change_notes|""}',
                                                                          suspend_period:
                                                                            '#{formDataRaw.suspension_duration}',
                                                                        },
                                                                        slicePath:
                                                                          'payload',
                                                                      },
                                                                    ],
                                                                    async: true,
                                                                    asyncLoadStart:
                                                                      true,
                                                                  },
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'closeModal',
                                                                    async: true,
                                                                    asyncLoadEnd:
                                                                      true,
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        },
                                                      ],
                                                    },
                                                  ],
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      },
                                    ],
                                  },
                                ],
                              },
                            ],
                          },
                          {
                            caseName: 'default',
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'log',
                                payload: [
                                  'No action can be performed for this option...',
                                ],
                              },
                            ],
                          },
                        ],
                      },
                      async: true,
                    },
                  ],
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '70%',
                    },
                  },
                },
              ],
            },
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
          },
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Panel List',
                options: {
                  format: 'heading',
                  type: 'section-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '4rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          layout: {
            display: 'grid',
            gridTemplateColumns: '1fr 3fr 1fr',
            justifyItems: 'center',
          },
          props: {
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                gridColumnStart: 2,
                width: '100%',
              },
              controls: [
                {
                  type: 'activation-buttons',
                  name: 'activation_buttons',
                  items: `js:{
                    const companies = sp_enums.companies.filter(
                      company => sp_profile.companies.find(org => org.client_id === company.id)
                    );

                    return companies.map(company => {
                      const org = sp_profile.companies.find(org => org.client_id === company.id);
                      return {
                        ...company,
                        ...org,
                        label: company.name,
                        tenant_id: company.id,
                        name: "panel_activation_status_" + company?.short_name + "_active"
                      };
                    })
                  }`,
                  itemLabelProp: 'label',
                  itemValueProp: 'active',
                  states: `js:{sp_enums.client_sub_active_states.filter(itm => itm.id !== 3)}`,
                  valueProp: 'id',
                  nameProp: 'name',
                  divider: true,
                  submitOnChange: false,
                  transparentBackground: true,
                  onChange: [
                    {
                      type: 'clientAction',
                      action: 'log',
                      payload: [`Changed client subscription status`, '@param'],
                      async: true,
                    },
                    {
                      type: 'clientAction',
                      action: 'updateStore',
                      payload: [{ current_item: '@param' }],
                      async: true,
                    },
                    {
                      type: 'clientAction',
                      action: 'switch',
                      payload: {
                        pathToValue: 'active',
                        cases: [
                          {
                            caseName: 0,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'switch',
                                payload: {
                                  pathToValue: 'prev_active',
                                  cases: [
                                    {
                                      caseName: 0,
                                      actions: [
                                        {
                                          type: 'clientAction',
                                          action: 'triggerModal',
                                          payload: [
                                            {
                                              display: true,
                                              type: 'warning',
                                              layout: {},
                                              onEnter: [],
                                              onLeave: [],
                                              fragments: [
                                                {
                                                  component: 'Text',
                                                  props: {
                                                    textItems: [
                                                      {
                                                        text: 'Warning',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'page-heading',
                                                        },
                                                      },
                                                      {
                                                        text: 'SP already <b>Disabled</b> for this panel.',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          style: {
                                                            paddingTop: '2rem',
                                                          },
                                                        },
                                                      },
                                                    ],
                                                  },
                                                  layout: {
                                                    display: 'grid',
                                                    gridAutoFlow: 'row',
                                                    justifyItems: 'center',
                                                  },
                                                },
                                                {
                                                  component: 'ButtonRow',
                                                  layout: {
                                                    width: 'fit-content',
                                                    margin: '0 auto',
                                                  },
                                                  props: {
                                                    buttons: [
                                                      {
                                                        btnValue: 'Okay',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                        ],
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                              navs: [],
                                            },
                                          ],
                                          param: '@param',
                                          debounce: 5,
                                          async: true,
                                        },
                                      ],
                                      param: '@param',
                                      debounce: 5,
                                      async: true,
                                    },
                                    {
                                      caseName: 'default',
                                      actions: [
                                        {
                                          type: 'clientAction',
                                          action: 'triggerModal',
                                          payload: [
                                            {
                                              display: true,
                                              type: 'warning',
                                              heading: 'Caution',
                                              headingType: 'page-heading',
                                              layout: {
                                                display: 'grid',
                                                gridAutoFlow: 'row',
                                                rowGap: '2rem',
                                                columnGap: '1rem',
                                                justifyItems: 'center',
                                                alignContent: 'space-around',
                                                height: 'auto',
                                                paddingTop: '2rem',
                                                paddingBottom: '2rem',
                                                width: '100%',
                                              },
                                              onEnter: [],
                                              onLeave: [],
                                              onClose: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          '@param:{name}',
                                                        defaultValue:
                                                          '@param:{prev_active}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'panel_activation_status_state_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'panel_activation_status_state_change_notes',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'panel_status_changed',
                                                  ],
                                                },
                                              ],
                                              fragments: [
                                                {
                                                  component: 'Text',
                                                  props: {
                                                    textItems: [
                                                      {
                                                        text: 'You are about to change this SP’s status to <b>Disable</b> on this panel and they will not be able to receive pings.',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          layout: {
                                                            paddingTop: '2rem',
                                                          },
                                                        },
                                                      },
                                                      {
                                                        text: "Are you sure you want to change SP's status?",
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          layout: {
                                                            paddingTop: '2rem',
                                                          },
                                                        },
                                                      },
                                                    ],
                                                  },
                                                  layout: {
                                                    justifyItems: 'center',
                                                    display: 'grid',
                                                    gap: '1rem',
                                                    textAlign: 'center',
                                                    maxWidth: '712px',
                                                  },
                                                },
                                                {
                                                  component: 'ButtonRow',
                                                  layout: {
                                                    width: 'fit-content',
                                                    margin: 'auto',
                                                  },
                                                  props: {
                                                    buttons: [
                                                      {
                                                        btnValue: 'No, cancel',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'resetFields',
                                                            payload: {
                                                              fields: [
                                                                {
                                                                  fieldName:
                                                                    '@param:{name}',
                                                                  defaultValue:
                                                                    '@param:{prev_active}',
                                                                },
                                                                {
                                                                  fieldName:
                                                                    'panel_activation_status_state_change_reason',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                                {
                                                                  fieldName:
                                                                    'panel_activation_status_state_change_notes',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                              ],
                                                            },
                                                            async: true,
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'clearStore',
                                                            payload: [
                                                              'panel_status_changed',
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                      {
                                                        btnValue:
                                                          'Yes, continue',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'triggerModal',
                                                            payload: [
                                                              {
                                                                display: true,
                                                                type: 'warning',
                                                                heading:
                                                                  'Changing Panel Status',
                                                                headingType:
                                                                  'page-heading',
                                                                layout: {},
                                                                onEnter: [],
                                                                onLeave: [],
                                                                onClose: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            '@param:{name}',
                                                                          defaultValue:
                                                                            '@param:{prev_active}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'panel_activation_status_state_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'panel_activation_status_state_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                ],
                                                                fragments: [
                                                                  {
                                                                    component:
                                                                      'FormBuilder',
                                                                    props: {
                                                                      defaultValues:
                                                                        {
                                                                          disable_reason:
                                                                            '',
                                                                          disable_notes:
                                                                            '',
                                                                        },
                                                                      config: {
                                                                        style: {
                                                                          display:
                                                                            'grid',
                                                                          gridAutoFlow:
                                                                            'row',
                                                                          rowGap:
                                                                            '2rem',
                                                                          columnGap:
                                                                            '1rem',
                                                                          justifyItems:
                                                                            'center',
                                                                          alignContent:
                                                                            'space-around',
                                                                          height:
                                                                            'auto',
                                                                          paddingTop:
                                                                            '2rem',
                                                                          paddingBottom:
                                                                            '2rem',
                                                                          width:
                                                                            '100%',
                                                                        },
                                                                        controls:
                                                                          [
                                                                            {
                                                                              type: 'single-select',
                                                                              name: 'panel_activation_status_state_change_reason',
                                                                              label:
                                                                                'Reason for status change',
                                                                              placeholder:
                                                                                'Select a Reason',
                                                                              labelProp:
                                                                                'name',
                                                                              valueProp:
                                                                                'id',
                                                                              validation:
                                                                                {
                                                                                  required:
                                                                                    {
                                                                                      value:
                                                                                        true,
                                                                                      message:
                                                                                        'Please provide a reason for status change',
                                                                                    },
                                                                                },
                                                                              options:
                                                                                {
                                                                                  source:
                                                                                    'literal',
                                                                                  data: 'js:{sp_enums.reasons_panel.filter(i => i.panel_active === 0)}',
                                                                                },
                                                                              notSearchable:
                                                                                true,
                                                                              css: {
                                                                                wrapper:
                                                                                  {
                                                                                    width:
                                                                                      '100%',
                                                                                  },
                                                                              },
                                                                            },
                                                                            {
                                                                              type: 'textarea',
                                                                              name: `panel_activation_status_state_change_notes`,
                                                                              label:
                                                                                'Notes',
                                                                              rows: 10,
                                                                              validation:
                                                                                {
                                                                                  required:
                                                                                    {
                                                                                      value:
                                                                                        true,
                                                                                      message:
                                                                                        'This field is required',
                                                                                    },
                                                                                },
                                                                              css: {
                                                                                wrapper:
                                                                                  {
                                                                                    width:
                                                                                      '100%',
                                                                                  },
                                                                              },
                                                                            },
                                                                          ],
                                                                      },
                                                                    },
                                                                    layout: {
                                                                      justifyItems:
                                                                        'center',
                                                                      display:
                                                                        'grid',
                                                                    },
                                                                  },
                                                                  {
                                                                    component:
                                                                      'ButtonRow',
                                                                    layout: {
                                                                      width:
                                                                        'fit-content',
                                                                      margin:
                                                                        'auto',
                                                                    },
                                                                    props: {
                                                                      buttons: [
                                                                        {
                                                                          btnValue:
                                                                            'Cancel change',
                                                                          onClick:
                                                                            [
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'resetFields',
                                                                                payload:
                                                                                  {
                                                                                    fields:
                                                                                      [
                                                                                        {
                                                                                          fieldName:
                                                                                            '@param:{name}',
                                                                                          defaultValue:
                                                                                            '@param:{prev_active}',
                                                                                        },
                                                                                        {
                                                                                          fieldName:
                                                                                            'panel_activation_status_state_change_reason',
                                                                                          defaultValue:
                                                                                            undefined,
                                                                                        },
                                                                                        {
                                                                                          fieldName:
                                                                                            'panel_activation_status_state_change_notes',
                                                                                          defaultValue:
                                                                                            undefined,
                                                                                        },
                                                                                      ],
                                                                                  },
                                                                              },
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'closeModal',
                                                                              },
                                                                            ],
                                                                        },
                                                                        {
                                                                          btnValue:
                                                                            'Change Status',
                                                                          disabledWhen: `!$form.panel_activation_status_state_change_reason`,
                                                                          onClick:
                                                                            [
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'triggerFetchCall',
                                                                                payload:
                                                                                  [
                                                                                    {
                                                                                      // key: 'sp_profile_resp',
                                                                                      method:
                                                                                        'POST',
                                                                                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_client_subscription',
                                                                                      body: {
                                                                                        active: 0,
                                                                                        sp_id:
                                                                                          '{sp_profile.id}',
                                                                                        client_id:
                                                                                          '@param:{client_id}',
                                                                                        reason_id:
                                                                                          '{formDataRaw.panel_activation_status_state_change_reason}',
                                                                                        detailed_reason:
                                                                                          '{formDataRaw.panel_activation_status_state_change_notes|""}',
                                                                                      },
                                                                                      // slicePath:
                                                                                      //   'payload',
                                                                                    },
                                                                                    {
                                                                                      key: 'sp_profile',
                                                                                      method:
                                                                                        'POST',
                                                                                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
                                                                                      body: {
                                                                                        sp_id:
                                                                                          '#{sp_profile.id}',
                                                                                      },
                                                                                      slicePath:
                                                                                        'payload',
                                                                                    },
                                                                                  ],
                                                                                async:
                                                                                  true,
                                                                                asyncLoadStart:
                                                                                  true,
                                                                              },
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'closeModal',
                                                                                async:
                                                                                  true,
                                                                                asyncLoadEnd:
                                                                                  true,
                                                                              },
                                                                            ],
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                            },
                                          ],
                                          param: '@param',
                                          debounce: 5,
                                          async: true,
                                        },
                                      ],
                                      param: '@param',
                                      debounce: 5,
                                      async: true,
                                    },
                                  ],
                                },
                                param: '@param',
                              },
                            ],
                          },
                          {
                            caseName: 1,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'switch',
                                payload: {
                                  pathToValue: `prev_active`,
                                  cases: [
                                    {
                                      caseName: 1,
                                      actions: [
                                        {
                                          type: 'clientAction',
                                          action: 'triggerModal',
                                          payload: [
                                            {
                                              display: true,
                                              type: 'warning',
                                              layout: {},
                                              onEnter: [],
                                              onLeave: [],
                                              fragments: [
                                                {
                                                  component: 'Text',
                                                  props: {
                                                    textItems: [
                                                      {
                                                        text: 'Warning',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'page-heading',
                                                        },
                                                      },
                                                      {
                                                        text: 'SP already <b>Enabled</b> for this panel.',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          style: {
                                                            paddingTop: '2rem',
                                                          },
                                                        },
                                                      },
                                                    ],
                                                  },
                                                  layout: {
                                                    display: 'grid',
                                                    gridAutoFlow: 'row',
                                                    justifyItems: 'center',
                                                  },
                                                },
                                                {
                                                  component: 'ButtonRow',
                                                  layout: {
                                                    width: 'fit-content',
                                                    margin: '0 auto',
                                                  },
                                                  props: {
                                                    buttons: [
                                                      {
                                                        btnValue: 'Okay',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                        ],
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                              navs: [],
                                            },
                                          ],
                                          param: '@param',
                                          debounce: 5,
                                          async: true,
                                        },
                                      ],
                                      param: '@param',
                                      debounce: 5,
                                      async: true,
                                    },
                                    {
                                      caseName: 'default',
                                      actions: [
                                        {
                                          type: 'clientAction',
                                          action: 'triggerModal',
                                          payload: [
                                            {
                                              display: true,
                                              type: 'warning',
                                              heading: 'Caution',
                                              headingType: 'page-heading',
                                              layout: {
                                                display: 'grid',
                                                gridAutoFlow: 'row',
                                                rowGap: '2rem',
                                                columnGap: '1rem',
                                                justifyItems: 'center',
                                                alignContent: 'space-around',
                                                height: 'auto',
                                                paddingTop: '2rem',
                                                paddingBottom: '2rem',
                                                width: '100%',
                                              },
                                              onEnter: [],
                                              onLeave: [],
                                              onClose: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          '@param:{name}',
                                                        defaultValue:
                                                          '@param:{prev_active}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'panel_activation_status_state_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'panel_activation_status_state_change_notes',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'panel_status_changed',
                                                  ],
                                                },
                                              ],
                                              fragments: [
                                                {
                                                  component: 'Text',
                                                  props: {
                                                    textItems: [
                                                      {
                                                        text: 'You are about to change this SP’s status to <b>Active</b> on this panel and they will be able to receive pings.',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          layout: {
                                                            paddingTop: '2rem',
                                                          },
                                                        },
                                                      },
                                                      {
                                                        text: "Are you sure you want to change SP's status?",
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          layout: {
                                                            paddingTop: '2rem',
                                                          },
                                                        },
                                                      },
                                                    ],
                                                  },
                                                  layout: {
                                                    justifyItems: 'center',
                                                    display: 'grid',
                                                    gap: '1rem',
                                                    textAlign: 'center',
                                                    maxWidth: '712px',
                                                  },
                                                },
                                                {
                                                  component: 'ButtonRow',
                                                  layout: {
                                                    width: 'fit-content',
                                                    margin: 'auto',
                                                  },
                                                  props: {
                                                    buttons: [
                                                      {
                                                        btnValue: 'No, cancel',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'resetFields',
                                                            payload: {
                                                              fields: [
                                                                {
                                                                  fieldName:
                                                                    '@param:{name}',
                                                                  defaultValue:
                                                                    '@param:{prev_active}',
                                                                },
                                                                {
                                                                  fieldName:
                                                                    'panel_activation_status_state_change_reason',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                                {
                                                                  fieldName:
                                                                    'panel_activation_status_state_change_notes',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                              ],
                                                            },
                                                            async: true,
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'clearStore',
                                                            payload: [
                                                              'panel_status_changed',
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                      {
                                                        btnValue:
                                                          'Yes, continue',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'triggerModal',
                                                            payload: [
                                                              {
                                                                display: true,
                                                                type: 'warning',
                                                                heading:
                                                                  'Changing Panel Status',
                                                                headingType:
                                                                  'page-heading',
                                                                layout: {},
                                                                onEnter: [],
                                                                onLeave: [],
                                                                onClose: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            '@param:{name}',
                                                                          defaultValue:
                                                                            '@param:{prev_active}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'panel_activation_status_state_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'panel_activation_status_state_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                ],
                                                                fragments: [
                                                                  {
                                                                    component:
                                                                      'FormBuilder',
                                                                    props: {
                                                                      defaultValues:
                                                                        {
                                                                          disable_reason:
                                                                            '',
                                                                          disable_notes:
                                                                            '',
                                                                        },
                                                                      config: {
                                                                        style: {
                                                                          display:
                                                                            'grid',
                                                                          gridAutoFlow:
                                                                            'row',
                                                                          rowGap:
                                                                            '2rem',
                                                                          columnGap:
                                                                            '1rem',
                                                                          justifyItems:
                                                                            'center',
                                                                          alignContent:
                                                                            'space-around',
                                                                          height:
                                                                            'auto',
                                                                          paddingTop:
                                                                            '2rem',
                                                                          paddingBottom:
                                                                            '2rem',
                                                                          width:
                                                                            '100%',
                                                                        },
                                                                        controls:
                                                                          [
                                                                            {
                                                                              type: 'single-select',
                                                                              name: 'panel_activation_status_state_change_reason',
                                                                              label:
                                                                                'Reason for status change',
                                                                              placeholder:
                                                                                'Select a Reason',
                                                                              labelProp:
                                                                                'name',
                                                                              valueProp:
                                                                                'id',
                                                                              validation:
                                                                                {
                                                                                  required:
                                                                                    {
                                                                                      value:
                                                                                        true,
                                                                                      message:
                                                                                        'Please provide reason for disabling SP.',
                                                                                    },
                                                                                },
                                                                              options:
                                                                                {
                                                                                  source:
                                                                                    'literal',
                                                                                  data: 'js:{sp_enums.reasons_panel.filter(i => i.panel_active === 1)}',
                                                                                },
                                                                              notSearchable:
                                                                                true,
                                                                              css: {
                                                                                wrapper:
                                                                                  {
                                                                                    width:
                                                                                      '100%',
                                                                                  },
                                                                              },
                                                                            },
                                                                            {
                                                                              type: 'textarea',
                                                                              name: `panel_activation_status_state_change_notes`,
                                                                              label:
                                                                                'Notes',
                                                                              rows: 10,
                                                                              css: {
                                                                                wrapper:
                                                                                  {
                                                                                    width:
                                                                                      '100%',
                                                                                  },
                                                                              },
                                                                            },
                                                                          ],
                                                                      },
                                                                    },
                                                                    layout: {
                                                                      justifyItems:
                                                                        'center',
                                                                      display:
                                                                        'grid',
                                                                    },
                                                                  },
                                                                  {
                                                                    component:
                                                                      'ButtonRow',
                                                                    layout: {
                                                                      width:
                                                                        'fit-content',
                                                                      margin:
                                                                        'auto',
                                                                    },
                                                                    props: {
                                                                      buttons: [
                                                                        {
                                                                          btnValue:
                                                                            'Cancel change',
                                                                          onClick:
                                                                            [
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'resetFields',
                                                                                payload:
                                                                                  {
                                                                                    fields:
                                                                                      [
                                                                                        {
                                                                                          fieldName:
                                                                                            '@param:{name}',
                                                                                          defaultValue:
                                                                                            '@param:{prev_active}',
                                                                                        },
                                                                                        {
                                                                                          fieldName:
                                                                                            'panel_activation_status_state_change_reason',
                                                                                          defaultValue:
                                                                                            undefined,
                                                                                        },
                                                                                        {
                                                                                          fieldName:
                                                                                            'panel_activation_status_state_change_notes',
                                                                                          defaultValue:
                                                                                            undefined,
                                                                                        },
                                                                                      ],
                                                                                  },
                                                                              },
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'closeModal',
                                                                              },
                                                                            ],
                                                                        },
                                                                        {
                                                                          btnValue:
                                                                            'Change status',
                                                                          disabledWhen: `!$form.panel_activation_status_state_change_reason`,
                                                                          onClick:
                                                                            [
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'triggerFetchCall',
                                                                                payload:
                                                                                  [
                                                                                    {
                                                                                      // key: 'sp_profile_resp',
                                                                                      method:
                                                                                        'POST',
                                                                                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_client_subscription',
                                                                                      body: {
                                                                                        active: 1,
                                                                                        sp_id:
                                                                                          '{sp_profile.id}',
                                                                                        client_id:
                                                                                          '@param:{client_id}',
                                                                                        reason_id:
                                                                                          '{formDataRaw.panel_activation_status_state_change_reason}',
                                                                                        detailed_reason:
                                                                                          '{formDataRaw.panel_activation_status_state_change_notes|""}',
                                                                                      },
                                                                                      // slicePath:
                                                                                      //   'payload',
                                                                                    },
                                                                                    {
                                                                                      key: 'sp_profile',
                                                                                      method:
                                                                                        'POST',
                                                                                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
                                                                                      body: {
                                                                                        sp_id:
                                                                                          '#{sp_profile.id}',
                                                                                      },
                                                                                      slicePath:
                                                                                        'payload',
                                                                                    },
                                                                                  ],
                                                                                async:
                                                                                  true,
                                                                                asyncLoadStart:
                                                                                  true,
                                                                              },
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'closeModal',
                                                                                async:
                                                                                  true,
                                                                                asyncLoadEnd:
                                                                                  true,
                                                                              },
                                                                            ],
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                            },
                                          ],
                                          param: '@param',
                                          debounce: 5,
                                          async: true,
                                        },
                                      ],
                                      param: '@param',
                                      debounce: 5,
                                      async: true,
                                    },
                                  ],
                                },
                                param: '@param',
                              },
                            ],
                          },
                          {
                            caseName: 2,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'switch',
                                payload: {
                                  pathToValue: `prev_active`,
                                  cases: [
                                    {
                                      caseName: 2,
                                      actions: [
                                        {
                                          type: 'clientAction',
                                          action: 'triggerModal',
                                          payload: [
                                            {
                                              display: true,
                                              type: 'warning',
                                              layout: {},
                                              onEnter: [],
                                              onLeave: [],
                                              fragments: [
                                                {
                                                  component: 'Text',
                                                  props: {
                                                    textItems: [
                                                      {
                                                        text: 'Warning',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'page-heading',
                                                        },
                                                      },
                                                      {
                                                        text: 'SP already in <b>Pending</b> for this panel.',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          style: {
                                                            paddingTop: '2rem',
                                                          },
                                                        },
                                                      },
                                                    ],
                                                  },
                                                  layout: {
                                                    display: 'grid',
                                                    gridAutoFlow: 'row',
                                                    justifyItems: 'center',
                                                  },
                                                },
                                                {
                                                  component: 'ButtonRow',
                                                  layout: {
                                                    width: 'fit-content',
                                                    margin: '0 auto',
                                                  },
                                                  props: {
                                                    buttons: [
                                                      {
                                                        btnValue: 'Okay',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                        ],
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                              navs: [],
                                            },
                                          ],
                                          param: '@param',
                                          debounce: 5,
                                          async: true,
                                        },
                                      ],
                                      param: '@param',
                                      debounce: 5,
                                      async: true,
                                    },
                                    {
                                      caseName: 'default',
                                      actions: [
                                        {
                                          type: 'clientAction',
                                          action: 'triggerModal',
                                          payload: [
                                            {
                                              display: true,
                                              type: 'warning',
                                              heading: 'Caution',
                                              headingType: 'page-heading',
                                              layout: {
                                                display: 'grid',
                                                gridAutoFlow: 'row',
                                                rowGap: '2rem',
                                                columnGap: '1rem',
                                                justifyItems: 'center',
                                                alignContent: 'space-around',
                                                height: 'auto',
                                                paddingTop: '2rem',
                                                paddingBottom: '2rem',
                                                width: '100%',
                                              },
                                              onEnter: [],
                                              onLeave: [],
                                              onClose: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          '@param:{name}',
                                                        defaultValue:
                                                          '@param:{prev_active}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'panel_activation_status_state_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'panel_activation_status_state_change_notes',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'panel_status_changed',
                                                  ],
                                                },
                                              ],
                                              fragments: [
                                                {
                                                  component: 'Text',
                                                  props: {
                                                    textItems: [
                                                      {
                                                        text: 'You are about to change this SP’s status to <b>Provisional</b> on this panel and they will not be able to receive pings.',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          layout: {
                                                            paddingTop: '2rem',
                                                          },
                                                        },
                                                      },
                                                      {
                                                        text: "Are you sure you want to change SP's status?",
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          layout: {
                                                            paddingTop: '2rem',
                                                          },
                                                        },
                                                      },
                                                    ],
                                                  },
                                                  layout: {
                                                    justifyItems: 'center',
                                                    display: 'grid',
                                                    gap: '1rem',
                                                    textAlign: 'center',
                                                    maxWidth: '712px',
                                                  },
                                                },
                                                {
                                                  component: 'ButtonRow',
                                                  layout: {
                                                    width: 'fit-content',
                                                    margin: 'auto',
                                                  },
                                                  props: {
                                                    buttons: [
                                                      {
                                                        btnValue: 'No, cancel',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'resetFields',
                                                            payload: {
                                                              fields: [
                                                                {
                                                                  fieldName:
                                                                    '@param:{name}',
                                                                  defaultValue:
                                                                    '@param:{prev_active}',
                                                                },
                                                                {
                                                                  fieldName:
                                                                    'panel_activation_status_state_change_reason',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                                {
                                                                  fieldName:
                                                                    'panel_activation_status_state_change_notes',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                              ],
                                                            },
                                                            async: true,
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'clearStore',
                                                            payload: [
                                                              'panel_status_changed',
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                      {
                                                        btnValue:
                                                          'Yes, continue',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'triggerModal',
                                                            payload: [
                                                              {
                                                                display: true,
                                                                type: 'warning',
                                                                heading:
                                                                  'Changing Panel Status',
                                                                headingType:
                                                                  'page-heading',
                                                                layout: {},
                                                                onEnter: [],
                                                                onLeave: [],
                                                                onClose: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            '@param:{name}',
                                                                          defaultValue:
                                                                            '@param:{prev_active}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'panel_activation_status_state_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'panel_activation_status_state_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                ],
                                                                fragments: [
                                                                  {
                                                                    component:
                                                                      'FormBuilder',
                                                                    props: {
                                                                      defaultValues:
                                                                        {
                                                                          panel_activation_status_state_change_reason:
                                                                            '',
                                                                          panel_activation_status_state_change_notes:
                                                                            '',
                                                                        },
                                                                      config: {
                                                                        style: {
                                                                          display:
                                                                            'grid',
                                                                          gridAutoFlow:
                                                                            'row',
                                                                          rowGap:
                                                                            '2rem',
                                                                          columnGap:
                                                                            '1rem',
                                                                          justifyItems:
                                                                            'center',
                                                                          alignContent:
                                                                            'space-around',
                                                                          height:
                                                                            'auto',
                                                                          paddingTop:
                                                                            '2rem',
                                                                          paddingBottom:
                                                                            '2rem',
                                                                          width:
                                                                            '100%',
                                                                        },
                                                                        controls:
                                                                          [
                                                                            {
                                                                              type: 'single-select',
                                                                              name: 'panel_activation_status_state_change_reason',
                                                                              label:
                                                                                'Reason for status change',
                                                                              placeholder:
                                                                                'Select a Reason',
                                                                              labelProp:
                                                                                'name',
                                                                              valueProp:
                                                                                'id',
                                                                              validation:
                                                                                {
                                                                                  required:
                                                                                    {
                                                                                      value:
                                                                                        true,
                                                                                      message:
                                                                                        'Please provide a reason for status change',
                                                                                    },
                                                                                },
                                                                              options:
                                                                                {
                                                                                  source:
                                                                                    'literal',
                                                                                  data: 'js:{sp_enums.reasons_panel.filter(i => i.panel_active === 2)}',
                                                                                },
                                                                              notSearchable:
                                                                                true,
                                                                              css: {
                                                                                wrapper:
                                                                                  {
                                                                                    width:
                                                                                      '100%',
                                                                                  },
                                                                              },
                                                                            },
                                                                            {
                                                                              type: 'textarea',
                                                                              name: `panel_activation_status_state_change_notes`,
                                                                              label:
                                                                                'Notes',
                                                                              rows: 10,
                                                                              css: {
                                                                                wrapper:
                                                                                  {
                                                                                    width:
                                                                                      '100%',
                                                                                  },
                                                                              },
                                                                            },
                                                                          ],
                                                                      },
                                                                    },
                                                                    layout: {
                                                                      justifyItems:
                                                                        'center',
                                                                      display:
                                                                        'grid',
                                                                    },
                                                                  },
                                                                  {
                                                                    component:
                                                                      'ButtonRow',
                                                                    layout: {
                                                                      width:
                                                                        'fit-content',
                                                                      margin:
                                                                        'auto',
                                                                    },
                                                                    props: {
                                                                      buttons: [
                                                                        {
                                                                          btnValue:
                                                                            'Cancel change',
                                                                          onClick:
                                                                            [
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'resetFields',
                                                                                payload:
                                                                                  {
                                                                                    fields:
                                                                                      [
                                                                                        {
                                                                                          fieldName:
                                                                                            '@param:{name}',
                                                                                          defaultValue:
                                                                                            '@param:{prev_active}',
                                                                                        },
                                                                                        {
                                                                                          fieldName:
                                                                                            'panel_activation_status_state_change_reason',
                                                                                          defaultValue:
                                                                                            undefined,
                                                                                        },
                                                                                        {
                                                                                          fieldName:
                                                                                            'panel_activation_status_state_change_notes',
                                                                                          defaultValue:
                                                                                            undefined,
                                                                                        },
                                                                                      ],
                                                                                  },
                                                                              },
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'closeModal',
                                                                              },
                                                                            ],
                                                                        },
                                                                        {
                                                                          btnValue:
                                                                            'Change status',
                                                                          disabledWhen: `!$form.panel_activation_status_state_change_reason`,
                                                                          onClick:
                                                                            [
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'triggerFetchCall',
                                                                                payload:
                                                                                  [
                                                                                    {
                                                                                      // key: 'sp_profile_resp',
                                                                                      method:
                                                                                        'POST',
                                                                                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_client_subscription',
                                                                                      body: {
                                                                                        active: 2,
                                                                                        sp_id:
                                                                                          '{sp_profile.id}',
                                                                                        client_id:
                                                                                          '@param:{client_id}',
                                                                                        reason_id:
                                                                                          '{formDataRaw.panel_activation_status_state_change_reason}',
                                                                                        detailed_reason:
                                                                                          '{formDataRaw.panel_activation_status_state_change_notes|""}',
                                                                                      },
                                                                                      // slicePath:
                                                                                      //   'payload',
                                                                                    },
                                                                                    {
                                                                                      key: 'sp_profile',
                                                                                      method:
                                                                                        'POST',
                                                                                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
                                                                                      body: {
                                                                                        sp_id:
                                                                                          '#{sp_profile.id}',
                                                                                      },
                                                                                      slicePath:
                                                                                        'payload',
                                                                                    },
                                                                                  ],
                                                                                async:
                                                                                  true,
                                                                                asyncLoadStart:
                                                                                  true,
                                                                              },
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'closeModal',
                                                                                async:
                                                                                  true,
                                                                                asyncLoadEnd:
                                                                                  true,
                                                                              },
                                                                            ],
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                            },
                                          ],
                                          debounce: 5,
                                        },
                                      ],
                                      param: '@param',
                                      debounce: 5,
                                      async: true,
                                    },
                                  ],
                                },
                                param: '@param',
                              },
                            ],
                          },
                          {
                            caseName: 4,
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'switch',
                                payload: {
                                  pathToValue: `prev_active`,
                                  cases: [
                                    {
                                      caseName: 4,
                                      actions: [
                                        {
                                          type: 'clientAction',
                                          action: 'triggerModal',
                                          payload: [
                                            {
                                              display: true,
                                              type: 'warning',
                                              layout: {},
                                              onEnter: [],
                                              onLeave: [],
                                              fragments: [
                                                {
                                                  component: 'Text',
                                                  props: {
                                                    textItems: [
                                                      {
                                                        text: 'Warning',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'page-heading',
                                                        },
                                                      },
                                                      {
                                                        text: 'SP already <b>Suspended</b> for this panel.',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          style: {
                                                            paddingTop: '2rem',
                                                          },
                                                        },
                                                      },
                                                    ],
                                                  },
                                                  layout: {
                                                    display: 'grid',
                                                    gridAutoFlow: 'row',
                                                    justifyItems: 'center',
                                                  },
                                                },
                                                {
                                                  component: 'ButtonRow',
                                                  layout: {
                                                    width: 'fit-content',
                                                    margin: '0 auto',
                                                  },
                                                  props: {
                                                    buttons: [
                                                      {
                                                        btnValue: 'Okay',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                        ],
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                              navs: [],
                                            },
                                          ],
                                        },
                                      ],
                                      param: '@param',
                                      debounce: 5,
                                      async: true,
                                    },
                                    {
                                      caseName: 'default',
                                      actions: [
                                        {
                                          type: 'clientAction',
                                          action: 'triggerModal',
                                          payload: [
                                            {
                                              display: true,
                                              type: 'warning',
                                              heading: 'Caution',
                                              headingType: 'page-heading',
                                              layout: {
                                                display: 'grid',
                                                gridAutoFlow: 'row',
                                                rowGap: '2rem',
                                                columnGap: '1rem',
                                                justifyItems: 'center',
                                                alignContent: 'space-around',
                                                height: 'auto',
                                                paddingTop: '2rem',
                                                paddingBottom: '2rem',
                                                width: '100%',
                                              },
                                              onEnter: [],
                                              onLeave: [],
                                              onClose: [
                                                {
                                                  type: 'clientAction',
                                                  action: 'resetFields',
                                                  payload: {
                                                    fields: [
                                                      {
                                                        fieldName:
                                                          '@param:{name}',
                                                        defaultValue:
                                                          '@param:{prev_active}',
                                                      },
                                                      {
                                                        fieldName:
                                                          'panel_activation_status_state_change_reason',
                                                        defaultValue: undefined,
                                                      },
                                                      {
                                                        fieldName:
                                                          'panel_activation_status_state_change_notes',
                                                        defaultValue: undefined,
                                                      },
                                                    ],
                                                  },
                                                },
                                                {
                                                  type: 'clientAction',
                                                  action: 'clearStore',
                                                  payload: [
                                                    'panel_status_changed',
                                                  ],
                                                },
                                              ],
                                              fragments: [
                                                {
                                                  component: 'Text',
                                                  props: {
                                                    textItems: [
                                                      {
                                                        text: 'You are about to change this SP’s status to <b>Suspended</b> on this panel and they will not be able to receive pings.',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          layout: {
                                                            paddingTop: '2rem',
                                                            maxWidth: '712px',
                                                            textAlign: 'center',
                                                          },
                                                        },
                                                      },
                                                    ],
                                                  },
                                                  layout: {
                                                    justifyItems: 'center',
                                                    display: 'grid',
                                                    textAlign: 'center',
                                                    maxWidth: '712px',
                                                  },
                                                },
                                                {
                                                  component: 'Text',
                                                  props: {
                                                    textItems: [
                                                      {
                                                        text: 'Are you sure you want to suspend this SP?',
                                                        options: {
                                                          format: 'heading',
                                                          type: 'sub-heading',
                                                          layout: {
                                                            paddingTop: '2rem',
                                                            maxWidth: '712px',
                                                            textAlign: 'center',
                                                          },
                                                        },
                                                      },
                                                    ],
                                                  },
                                                  layout: {
                                                    justifyItems: 'center',
                                                    display: 'grid',
                                                    textAlign: 'center',
                                                    maxWidth: '712px',
                                                  },
                                                },
                                                {
                                                  component: 'ButtonRow',
                                                  layout: {
                                                    width: 'fit-content',
                                                    margin: 'auto',
                                                  },
                                                  props: {
                                                    buttons: [
                                                      {
                                                        btnValue: 'No, cancel',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'resetFields',
                                                            payload: {
                                                              fields: [
                                                                {
                                                                  fieldName:
                                                                    '@param:{name}',
                                                                  defaultValue:
                                                                    '@param:{prev_active}',
                                                                },
                                                                {
                                                                  fieldName:
                                                                    'panel_activation_status_state_change_reason',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                                {
                                                                  fieldName:
                                                                    'panel_activation_status_state_change_notes',
                                                                  defaultValue:
                                                                    undefined,
                                                                },
                                                              ],
                                                            },
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'clearStore',
                                                            payload: [
                                                              'panel_status_changed',
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                      {
                                                        btnValue:
                                                          'Yes, continue',
                                                        onClick: [
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'closeModal',
                                                          },
                                                          {
                                                            type: 'clientAction',
                                                            action:
                                                              'triggerModal',
                                                            payload: [
                                                              {
                                                                display: true,
                                                                type: 'warning',
                                                                heading:
                                                                  'Changing Panel Status',
                                                                headingType:
                                                                  'page-heading',
                                                                layout: {},
                                                                onEnter: [],
                                                                onLeave: [],
                                                                onClose: [
                                                                  {
                                                                    type: 'clientAction',
                                                                    action:
                                                                      'resetFields',
                                                                    payload: {
                                                                      fields: [
                                                                        {
                                                                          fieldName:
                                                                            '@param:{name}',
                                                                          defaultValue:
                                                                            '@param:{prev_active}',
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'panel_activation_status_state_change_reason',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                        {
                                                                          fieldName:
                                                                            'panel_activation_status_state_change_notes',
                                                                          defaultValue:
                                                                            undefined,
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                ],
                                                                fragments: [
                                                                  {
                                                                    component:
                                                                      'FormBuilder',
                                                                    props: {
                                                                      defaultValues:
                                                                        {
                                                                          panel_activation_status_state_change_reason:
                                                                            '',
                                                                          panel_activation_status_state_change_notes:
                                                                            '',
                                                                          suspension_duration:
                                                                            '',
                                                                        },
                                                                      config: {
                                                                        style: {
                                                                          display:
                                                                            'grid',
                                                                          gridAutoFlow:
                                                                            'row',
                                                                          rowGap:
                                                                            '2rem',
                                                                          columnGap:
                                                                            '1rem',
                                                                          justifyItems:
                                                                            'center',
                                                                          alignContent:
                                                                            'space-around',
                                                                          height:
                                                                            'auto',
                                                                          paddingTop:
                                                                            '2rem',
                                                                          paddingBottom:
                                                                            '2rem',
                                                                          width:
                                                                            '100%',
                                                                        },
                                                                        controls:
                                                                          [
                                                                            {
                                                                              type: 'single-select',
                                                                              name: 'suspension_duration',
                                                                              label:
                                                                                'Length of suspension',
                                                                              placeholder:
                                                                                'Select a time frame',
                                                                              labelProp:
                                                                                'name',
                                                                              valueProp:
                                                                                'days',
                                                                              validation:
                                                                                {
                                                                                  required:
                                                                                    {
                                                                                      value:
                                                                                        true,
                                                                                      message:
                                                                                        'Duration for suspension',
                                                                                    },
                                                                                },
                                                                              options:
                                                                                {
                                                                                  source:
                                                                                    'store',
                                                                                  storeDataPath:
                                                                                    'sp_enums.suspend_periods',
                                                                                },
                                                                              notSearchable:
                                                                                true,
                                                                              css: {
                                                                                wrapper:
                                                                                  {
                                                                                    width:
                                                                                      '100%',
                                                                                  },
                                                                              },
                                                                            },
                                                                            {
                                                                              type: 'single-select',
                                                                              name: 'panel_activation_status_state_change_reason',
                                                                              label:
                                                                                'Reason for status change',
                                                                              placeholder:
                                                                                'Select a Reason',
                                                                              labelProp:
                                                                                'name',
                                                                              valueProp:
                                                                                'id',
                                                                              validation:
                                                                                {
                                                                                  required:
                                                                                    {
                                                                                      value:
                                                                                        true,
                                                                                      message:
                                                                                        'Please provide a reason for status change',
                                                                                    },
                                                                                },
                                                                              options:
                                                                                {
                                                                                  source:
                                                                                    'literal',
                                                                                  data: 'js:{sp_enums.reasons_panel.filter(i => i.panel_active === 4)}',
                                                                                },
                                                                              notSearchable:
                                                                                true,
                                                                              css: {
                                                                                wrapper:
                                                                                  {
                                                                                    width:
                                                                                      '100%',
                                                                                  },
                                                                              },
                                                                            },
                                                                            {
                                                                              type: 'textarea',
                                                                              name: 'panel_activation_status_state_change_notes',
                                                                              label:
                                                                                'Notes',
                                                                              rows: 10,
                                                                              css: {
                                                                                wrapper:
                                                                                  {
                                                                                    width:
                                                                                      '100%',
                                                                                  },
                                                                              },
                                                                            },
                                                                          ],
                                                                      },
                                                                    },
                                                                    layout: {
                                                                      justifyItems:
                                                                        'center',
                                                                      display:
                                                                        'grid',
                                                                    },
                                                                  },
                                                                  {
                                                                    component:
                                                                      'ButtonRow',
                                                                    layout: {
                                                                      width:
                                                                        'fit-content',
                                                                      margin:
                                                                        'auto',
                                                                    },
                                                                    props: {
                                                                      buttons: [
                                                                        {
                                                                          btnValue:
                                                                            'Cancel change',
                                                                          onClick:
                                                                            [
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'resetFields',
                                                                                payload:
                                                                                  {
                                                                                    fields:
                                                                                      [
                                                                                        {
                                                                                          fieldName:
                                                                                            '@param:{name}',
                                                                                          defaultValue:
                                                                                            '@param:{prev_active}',
                                                                                        },
                                                                                        {
                                                                                          fieldName:
                                                                                            'panel_activation_status_state_change_reason',
                                                                                          defaultValue:
                                                                                            undefined,
                                                                                        },
                                                                                        {
                                                                                          fieldName:
                                                                                            'panel_activation_status_state_change_notes',
                                                                                          defaultValue:
                                                                                            undefined,
                                                                                        },
                                                                                      ],
                                                                                  },
                                                                              },
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'clearStore',
                                                                                payload:
                                                                                  [
                                                                                    'panel_status_changed',
                                                                                  ],
                                                                              },
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'closeModal',
                                                                              },
                                                                            ],
                                                                        },
                                                                        {
                                                                          btnValue:
                                                                            'Change status',
                                                                          disabledWhen:
                                                                            '!$store.formDataRaw.panel_activation_status_state_change_reason || !$store.formDataRaw.suspension_duration',
                                                                          onClick:
                                                                            [
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'triggerFetchCall',
                                                                                payload:
                                                                                  [
                                                                                    {
                                                                                      // key: 'sp_profile_resp',
                                                                                      method:
                                                                                        'POST',
                                                                                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_client_subscription',
                                                                                      body: {
                                                                                        active: 4,
                                                                                        sp_id:
                                                                                          '{sp_profile.id}',
                                                                                        client_id:
                                                                                          '@param:{client_id}',
                                                                                        reason_id:
                                                                                          '{formDataRaw.panel_activation_status_state_change_reason}',
                                                                                        detailed_reason:
                                                                                          '{formDataRaw.panel_activation_status_state_change_notes|""}',
                                                                                        suspend_period:
                                                                                          '#{formDataRaw.suspension_duration}',
                                                                                      },
                                                                                      // slicePath:
                                                                                      //   'payload',
                                                                                    },
                                                                                    {
                                                                                      key: 'sp_profile',
                                                                                      method:
                                                                                        'POST',
                                                                                      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
                                                                                      body: {
                                                                                        sp_id:
                                                                                          '#{sp_profile.id}',
                                                                                      },
                                                                                      slicePath:
                                                                                        'payload',
                                                                                    },
                                                                                  ],
                                                                                async:
                                                                                  true,
                                                                                asyncLoadStart:
                                                                                  true,
                                                                              },
                                                                              {
                                                                                type: 'clientAction',
                                                                                action:
                                                                                  'closeModal',
                                                                                async:
                                                                                  true,
                                                                                asyncLoadEnd:
                                                                                  true,
                                                                              },
                                                                            ],
                                                                        },
                                                                      ],
                                                                    },
                                                                  },
                                                                ],
                                                              },
                                                            ],
                                                          },
                                                        ],
                                                      },
                                                    ],
                                                  },
                                                },
                                              ],
                                            },
                                          ],
                                        },
                                      ],
                                      param: '@param',
                                      debounce: 5,
                                      async: true,
                                    },
                                  ],
                                },
                                param: '@param',
                              },
                            ],
                          },
                          {
                            caseName: 'default',
                            actions: [
                              {
                                type: 'clientAction',
                                action: 'log',
                                payload: [
                                  'No action can be performed for this option...',
                                ],
                              },
                            ],
                          },
                        ],
                      },
                      param: '@param',
                      async: true,
                    },
                  ],
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          disableComponentConfig: {
            disableCondition:
              'js:{formDataRaw.platform_status === 6 || formDataRaw.platform_status === 11}',
            noIconLabel: true,
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store.formDataRaw.platform_status !== $store.sp_profile.onboarding_state',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Caution',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                                  You are about to exit this flow with unsaved changes.
                                `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                                  Are you sure you want to go back to workflow without saving the changes?
                                `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'No',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Yes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/manage-sps/sps'],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/manage-sps/sps'],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Next',
          position: 'right',
          toScreen: '../details',
        },
      ],
    },

    // #region SPS/EDIT/DETAILS SCREEN
    details: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Status',
                path: '../status',
              },
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'ProfileHero',
          layout: {
            display: 'grid',
            justifyContent: 'center',
          },
          props: {
            fullname: '$sp_profile.details.name',
            subText: 'Registration number: ',
            username: '$sp_profile.details.co_reg',
            active: false,
            image: '$sp_profile.company_profile_picture',
            profileType: 'company',
            state: '$sp_profile.details.onboarding_state',
            showImgUpload: false,
          },
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Company details',
                options: {
                  format: 'heading',
                  type: 'section-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            // width: 'calc(100% - 226px - 56px)',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            bbbeee_certificate:
              "$company_documentation?find:item.purpose === 'Reg BBEEE certificate'",
            defaultValues: {
              trading_as: '$sp_profile.details.trading_as',
              bbeee: '$sp_profile.details.bbeee',
              co_reg: '$sp_profile.details.co_reg',
              name: '$sp_profile.details.name',
              company_type: '$sp_profile.details.company_type',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'co_reg',
                  label: 'Company registration',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  validation: {
                    required: {
                      value: true,
                      message: 'Registration number is required',
                    },
                    conditional: {
                      value: `$store.formDataRaw.company_type === 2 ? 'registration_number' : 'company_registration_number'`,
                      message: 'Registration number is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'name',
                  label: 'Company name',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  validation: {
                    required: {
                      value: true,
                      message: 'Name is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'trading_as',
                  label: 'Trading as',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  validation: {},
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'company_type',
                  label: 'Type of company',
                  state: 'display-only',
                  valueProp: 'id',
                  labelProp: 'name',
                  disabledWhen: true,
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.company_types',
                  },
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  validation: {},
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  dropdownScroll: true,
                  name: 'bbeee',
                  label: 'BBBEEE Level',
                  state: 'display-only',
                  labelProp: 'label',
                  valueProp: 'value',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  validation: {},
                  options: {
                    data: bbbeee,
                    source: 'literal',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'bbbeee_certificate',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg BBEEE certificate',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  disabledWhen: true,
                  fieldAccessPath: { view: 'details', edit: 'details' },
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            // width: 'calc(100% - 226px - 56px)',
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/manage-sps/sps'],
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../status',
        },
        {
          label: 'Next',
          position: 'right',
          toScreen: '../banking',
        },
      ],
    },
    // #endregion
    // #region SPS/EDIT/CONTACT SCREEN
    contact: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Status',
                path: '../status',
              },
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Company contact information',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              contact_primary: '$sp_profile.address.contact_primary',
              contact_secondary: '$sp_profile.address.contact_secondary',
              contact_person: '$sp_profile.address.contact_person',
              email_receiving: '$sp_profile.address.email_receiving',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'contact_person',
                  label: 'Primary Contact Person Name',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'contact_primary',
                  label: 'Primary Contact Number',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.phone_number.pattern,
                      message: validationRegex.phone_number.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'contact_secondary',
                  label: 'Secondary Contact Number',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    pattern: {
                      value: validationRegex.phone_number.pattern,
                      message: validationRegex.phone_number.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'email_receiving',
                  label: 'Email Address',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.email.pattern,
                      message: validationRegex.email.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
        {
          component: 'Separator',
          layout: { width: '50%', minWidth: '712px', margin: 'auto' },
          props: { height: 'thin' },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              physical_addr: '$sp_profile.address.physical_addr',
              physical_city: '$sp_profile.address.physical_city',
              physical_code: '$sp_profile.address.physical_code',
              physical_suburb: '$sp_profile.address.physical_suburb',
              province: '$sp_profile.address.province',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'physical_addr',
                  label: 'Company street address',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_suburb',
                  label: 'Company suburb',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  state: 'display-only',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_city',
                  label: 'Company city',
                  state: 'display-only',
                  disabledWhen: true,
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_code',
                  label: 'Company postal code',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'province',
                  label: 'Company province',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  dropdownScroll: true,
                  state: 'display-only',
                  validation: {
                    required: {
                      value: true,
                      message: 'Province is required',
                    },
                  },
                  labelProp: 'name',
                  valueProp: 'name',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.provinces',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
        {
          component: 'Separator',
          layout: { width: '50%', minWidth: '712px', margin: 'auto' },
          props: { height: 'thin' },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              postal_box: '$sp_profile.address.postal_box',
              postal_city: '$sp_profile.address.postal_city',
              postal_code: '$sp_profile.address.postal_code',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'postal_box',
                  label: 'Postal address',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'postal_city',
                  label: 'Postal address suburb/town',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'postal_code',
                  label: 'Postal address postal code',
                  state: 'display-only',
                  disabledWhen: true,
                  fieldAccessPath: { view: 'address', edit: 'address' },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          // toScreen: '/',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/manage-sps/sps'],
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../directors',
        },
        { label: 'Next', position: 'right', toScreen: '../work' },
      ],
    },
    // #endregion
    // #region SPS/EDIT/WORK SCREEN
    work: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Status',
                path: '../status',
              },
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Scope of work you accept',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            sp_associated_companies: '$sp_profile.companies?map:item.client_id',
            defaultValues: {
              skills: '$sp_profile.skills',
              after_hours: '$sp_profile.after_hours',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'multiselect-checklist',
                  name: 'skills',
                  label: 'Company skills',
                  state: 'display-only',
                  fieldAccessPath: {
                    view: 'skills',
                    edit: 'skills',
                    special: 'skills',
                  },
                  labelProp: 'name',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.skills',
                  },
                  checkedItems: 'sp_profile.skills',
                  maxColumns: 2,
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'radio-group',
                  name: 'after_hours',
                  fieldAccessPath: {
                    view: 'after_hours',
                    edit: 'after_hours',
                  },
                  disabledWhen: true,
                  label: 'Do you work after hours?',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  options: {
                    source: 'literal',
                    data: [
                      { label: 'Yes', value: true },
                      { label: 'No', value: false },
                    ],
                  },
                  size: 'small',
                  returnBoolean: true,
                  css: {
                    wrapper: {
                      justifySelf: 'start',
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'multiselect-checklist',
                  name: 'sp_associated_companies',
                  companies: '$sp_profile.companies',
                  disabledWhen: true,
                  fieldAccessPath: {
                    view: 'companies',
                    edit: 'companies',
                  },
                  label: 'Companies you would like to recieve work from',
                  state: 'display-only',
                  labelProp: 'name',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.companies',
                  },
                  checkedItems: 'sp_profile.companies',
                  checkedItemsTransformPath: 'client_id',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/manage-sps/sps'],
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../contact',
        },
        { label: 'Next', position: 'right', toScreen: '../areas' },
      ],
    },
    // #endregion
    // #region SPS/EDIT/AREAS SCREEN
    areas: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Status',
                path: '../status',
              },
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'FormBuilder',
          layout: {
            paddingTop: '2rem',
          },
          props: {
            operational_area: '$sp_profile.operational_area',
            defaultValues: {
              radius: '$sp_profile.operational_area.0.operating_range',
              jobLocation: '$sp_profile.operational_area.0.location',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: '1fr',
                rowGap: '15px',
                justifyItems: 'center',
              },
              controls: [
                {
                  type: 'radius', // Custom control type for OperationAreas
                  name: 'operational_area',
                  label: 'Operational Area',
                  disabledWhen: true,
                  instructions: "Service provider's work area radius",
                  marks: [
                    { value: 25, label: '25km' },
                    { value: 50, label: '50km' },
                    { value: 75, label: '75km' },
                    { value: 100, label: '100km' },
                    { value: 150, label: '150km' },
                    { value: 200, label: '200km' },
                    { value: 250, label: '250km' },
                  ],
                  css: { wrapper: { gridColumn: '1', gridRow: '1' } },
                },
              ],
            },
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          // toScreen: '/',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/manage-sps/sps'],
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../work',
        },
        { label: 'Next', position: 'right', toScreen: '../documentation' },
      ],
    },
    // #endregion
    // #region SPS/EDIT/DOCUMENTATION SCREEN
    documentation: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Status',
                path: '../status',
              },
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Uploaded documents',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'PDF only. Maximum 5Mb per document',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'DocumentCardList',
          props: {
            documents: `js:{
            const skill_certifications = company_documentation
                .filter(item => !!item.meta.certification)
                .reduce((acc, item) => {
                  const cert = item.meta.certification;
                  const key = cert.skill+"-"+cert.issuer;
                  if (!acc.has(key) || new Date(item.created) > new Date(acc.get(key).created)) {
                    acc.set(key, item);
                  }

                  return acc;
                }, new Map())
                .values();
              const certificatesArray = Array.from(skill_certifications).map(item => ({
                ...item,
                meta: {
                  'Certificate Issuer': sp_enums.issuers.find(issuer => issuer.id === item.meta.certification.issuer)?.name,
                  'Certificate Number': item.meta.certification.certificate_number,
                  'Certificate Issue Date': new Date(item.meta.certification.issued_at).toLocaleDateString(),
                  'Certificate Expiry Date': new Date(item.meta.certification.valid_until).toLocaleDateString(),
                },
              }));
              if(typeof certificatesArray !== 'object' && !Array.isArray(certificatesArray)) return []
              return certificatesArray || [];
            }`,
            isLandscape: true,
            sp_id: '$sp_profile.id',
            base_url_env_name: 'VITE_SP_SERVER',
            director_id: '$director.id',
            columns: 1,
            gap: '1rem',
            enableUpload: false,
          },
          layout: {
            alignSelf: 'center',
            paddingTop: '1rem',
            minWidth: '712px',
            width: '50%',
            justifySelf: 'center',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            co_reg_document:
              "$company_documentation?find:item.purpose === 'Reg Company registration'",
            pub_profile_document:
              "$company_documentation?find:item.purpose === 'Reg Public profile'",
            pub_liability_document:
              "$company_documentation?find:item.purpose === 'Reg Public liability insurance'",
            bbeee_cert_document:
              "$company_documentation?find:item.purpose === 'Reg BBEEE certificate'",
            sars_tax_document:
              "$company_documentation?find:item.purpose === 'Reg SARS Tax certificate'",
            proof_of_bank_account_document:
              "$company_documentation?find:item.purpose === 'Reg Proof of bank account'",
            vehicle_document:
              "$company_documentation?find:item.purpose === 'Reg Vehicle picture'",
            office_document:
              "$company_documentation?find:item.purpose === 'Reg Office picture'",
            staff_uniform_document:
              "$company_documentation?find:item.purpose === 'Reg Staff uniform picture'",
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'document-card',
                  name: 'co_reg_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Company registration',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'pub_profile_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Public profile',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'pub_liability_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Public liability insurance',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'bbeee_cert_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg BBEEE certificate',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'sars_tax_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg SARS Tax certificate',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'proof_of_bank_account_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Proof of bank account',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'vehicle_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Vehicle picture',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'office_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Office picture',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'staff_uniform_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Staff uniform picture',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/manage-sps/sps'],
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../areas',
        },
      ],
    },
    // #endregion
    // #region SPS/EDIT/DIRECTORS SCREEN
    directors: {
      layout: {},
      fetchCalls: [
        {
          key: 'directors',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_directors',
          body: { sp_id: '$object_id' },
          slicePath: 'payload',
        },
      ],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Status',
                path: '../status',
              },
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Enter all director names',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Changing director names requires approval',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'DirectorsListWithFileView',
          props: {
            directors: '$directors',
            filesUrl: '/api/v1/file_actions/director_get_files',
            removeDirectorUrl: '/api/v1/spaas_actions/remove_director',
            removeDirectorRedirectUrl: '/manage-sps/sps/edit/directors',
            disabledWhen: true,
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/manage-sps/sps'],
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: [
                'sp_profile',
                'company_documentation',
                'documentsNotFound',
                'derivedCompanies',
                'derivedOperationalArea',
                'directors',
                'originalValues',
              ],
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../banking',
        },
        { label: 'Next', position: 'right', toScreen: '../contact' },
      ],
    },
    // #endregion
    // #region SPS/EDIT/BANKING SCREEN
    banking: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Status',
                path: '../status',
              },
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Company banking details',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Changing bank details requires approval',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
                icon: {
                  type: 'alert-diamond',
                  size: 24,
                  strokeWidth: '1px',
                  color: '#FF9800',
                  style: { margin: '0 0 0 4px' },
                },
                iconPosition: 'right',
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            proof_of_bank_account_document:
              "$company_documentation?find:item.purpose === 'Reg Proof of bank account'",
            defaultValues: {
              b_acc_holder: '$sp_profile.financial.b_acc_holder',
              b_branch_name: '$sp_profile.financial.b_branch_name',
              b_acc_type: '$sp_profile.financial.b_acc_type',
              b_branch_code: '$sp_profile.financial.b_branch_code',
              b_bank_name: '$sp_profile.financial.b_bank_name',
              b_acc_no: '$sp_profile.financial.b_acc_no',
              vat_no: '$sp_profile.financial.vat_no',
              tax_no: '$sp_profile.financial.tax_no',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
                marginBottom: '6rem',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'b_acc_holder',
                  label: 'Bank account holder name',
                  icon: 'alarm-clock',
                  position: 'right',
                  state: 'display-only',
                  disabledWhen: true,
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'b_bank_name',
                  label: 'Bank name',
                  labelProp: 'name',
                  valueProp: 'id',
                  icon: 'alarm-clock',
                  dropdownScroll: true,
                  state: 'display-only',
                  disabledWhen: true,
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.banks',
                    labelProp: 'name',
                    valueProp: 'id',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'b_acc_no',
                  label: 'Bank account number',
                  icon: 'alarm-clock',
                  position: 'right',
                  state: 'display-only',
                  disabledWhen: true,
                  validation: {
                    pattern: {
                      value: validationRegex.bank_account_number.pattern,
                      message: validationRegex.bank_account_number.message,
                    },
                    minLength: {
                      value: 9,
                      message:
                        'Account number is invalid, please confirm banking details',
                    },
                    maxLength: {
                      value: 16,
                      message:
                        'Account number is invalid, please confirm banking details',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'b_acc_type',
                  label: 'Account type',
                  labelProp: 'name',
                  valueProp: 'id',
                  icon: 'alarm-clock',
                  state: 'display-only',
                  disabledWhen: true,
                  dropdownScroll: true,
                  validation: {
                    required: {
                      value: true,
                      message: 'Bank account type is required',
                    },
                  },
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.account_types',
                    labelProp: 'name',
                    valueProp: 'id',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'b_branch_name',
                  label: 'Branch name',
                  icon: 'alarm-clock',
                  position: 'right',
                  state: 'display-only',
                  disabledWhen: true,
                  validation: {
                    required: {
                      value: true,
                      message: 'Branch name is required',
                    },
                    pattern: {
                      value: validationRegex.name.pattern,
                      message: validationRegex.name.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'b_branch_code',
                  label: 'Branch code',
                  icon: 'alarm-clock',
                  state: 'display-only',
                  position: 'right',
                  disabledWhen: true,
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.branch_code.pattern,
                      message: validationRegex.branch_code.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'vat_no',
                  label: 'VAT registration number',
                  icon: 'alarm-clock',
                  position: 'right',
                  state: 'display-only',
                  disabledWhen: true,
                  validation: {
                    pattern: {
                      value: validationRegex.vat_number.pattern,
                      message: validationRegex.vat_number.message,
                    },
                  },
                  // TODO: How to close modal and refocus on control?
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'tax_no',
                  label: 'Tax number',
                  state: 'display-only',
                  disabledWhen: true,
                  validation: {
                    pattern: {
                      value: validationRegex.tax_number.pattern,
                      message: validationRegex.tax_number.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'proof_of_bank_account_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Proof of bank account',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  disabledWhen: true,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/manage-sps/sps/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/manage-sps/sps'],
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: [
                'sp_profile',
                'company_documentation',
                'documentsNotFound',
                'derivedCompanies',
                'derivedOperationalArea',
                'directors',
                'originalValues',
              ],
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../details',
        },
        { label: 'Next', position: 'right', toScreen: '../directors' },
      ],
    },
    // #endregion
    // #region SPS/EDIT/HISTORY SCREEN
    history: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Status',
                path: '../status',
              },
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'History',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Review all changes to this Service Provider below',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '4rem',
          },
        },
        {
          component: 'ArrayMapWithDivider',
          // props: {
          //   events: '$sp_history',
          // },
          props: {
            events: `js:{
              return sp_history.map(item => ({
                'Date & Time': item.timestamp.split('T')[0] + ' - ' + (item.timestamp.split('T')[1]?.split(':').slice(0,2).join(':') || ''),
                'Action': item.event_source,
                'Agent Name': item.event_initiator?.split(':').slice(1).join(':').trim() || item.event_initiator,
              }));
            }`,
          },
          layout: {},
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/manage-sps/sps'],
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: [
                'sp_profile',
                'company_documentation',
                'documentsNotFound',
                'derivedCompanies',
                'derivedOperationalArea',
                'directors',
                'originalValues',
              ],
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../details',
        },
        { label: 'Next', position: 'right', toScreen: '../directors' },
      ],
    },
    // #endregion
    // #endregion
    // #region SPS/EDIT/HISTORY SCREEN
    'dynamic-table-test': {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Status',
                path: '../status',
              },
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
              {
                name: 'History',
                path: '../history',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'History',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Review all changes to this Service Provider below',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '4rem',
          },
        },
        {
          component: 'ArrayMapWithDivider',
          // props: {
          //   events: '$sp_history',
          // },
          props: {
            events: `js:{
              return sp_history.map(item => ({
                'Date & Time': item.timestamp.split('T')[0] + ' - ' + (item.timestamp.split('T')[1]?.split(':').slice(0,2).join(':') || ''),
                'Action': item.event_source,
                'Agent Name': item.event_initiator?.split(':').slice(1).join(':').trim() || item.event_initiator,
              }));
            }`,
          },
          layout: {},
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'navigate',
              payload: ['/manage-sps/sps'],
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: [
                'sp_profile',
                'company_documentation',
                'documentsNotFound',
                'derivedCompanies',
                'derivedOperationalArea',
                'directors',
                'originalValues',
              ],
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../details',
        },
        { label: 'Next', position: 'right', toScreen: '../directors' },
      ],
    },
    // #endregion
  },
  /*
   * SECTION: SPS/EDIT ACTION PANELS
   * Add all action panel item configurations here
   */
  // #region SPS/EDIT ACTION PANELS
  actionPanels: [
    // #region SCRATCH PAD
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      // fetchCalls: [],
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
    // #endregion
  ],
  // #endregion
} satisfies StateConfig;
// #endregion
