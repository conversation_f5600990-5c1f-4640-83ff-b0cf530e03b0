$ vitest run src/lib/Engine/hooks/__tests__/useDataReadiness.test.tsx
[33mThe CJS build of Vite's Node API is deprecated. See https://vitejs.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
 Vitest  "cache.dir" is deprecated, use Vite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"
stderr | src/lib/Engine/hooks/__tests__/useDataReadiness.test.tsx > useDataReadiness > gates component by route, loader and store keys when listed
Warning: An update to Probe inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
    at Probe (/Users/<USER>/Developer/4SURE_Projects/ui-platform/ui-platform/src/lib/Engine/hooks/__tests__/useDataReadiness.test.tsx:18:112)

stderr | src/lib/Engine/hooks/__tests__/useDataReadiness.test.tsx > useDataReadiness > respects asyncLoading flag from useAsyncLoaderStore
Warning: An update to Probe inside a test was not wrapped in act(...).

When testing, code that causes React state updates should be wrapped into act(...):

act(() => {
  /* fire events that update state */
});
/* assert on the output */

This ensures that you're testing the behavior the user would see in the browser. Learn more at https://reactjs.org/link/wrap-tests-with-act
    at Probe (/Users/<USER>/Developer/4SURE_Projects/ui-platform/ui-platform/src/lib/Engine/hooks/__tests__/useDataReadiness.test.tsx:18:112)

⎯⎯⎯⎯⎯⎯⎯ Failed Tests 2 ⎯⎯⎯⎯⎯⎯⎯

 FAIL  |@4-sure/ui-platform| src/lib/Engine/hooks/__tests__/useDataReadiness.test.tsx > useDataReadiness > gates component by route, loader and store keys when listed
TestingLibraryElementError: Found multiple elements by: [data-testid="storeReady"]

Here are the matching elements:

Ignored nodes: comments, script, style
[36m<div[39m
  [33mdata-testid[39m=[32m"storeReady"[39m
[36m>[39m
  [0mtrue[0m
[36m</div>[39m

Ignored nodes: comments, script, style
[36m<div[39m
  [33mdata-testid[39m=[32m"storeReady"[39m
[36m>[39m
  [0mtrue[0m
[36m</div>[39m

(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).

Ignored nodes: comments, script, style
[36m<body>[39m
  [36m<div>[39m
    [36m<div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"ready"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"routeReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"loadersReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"storeReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"keys"[39m
      [36m>[39m
        [0mfiltersData,auth[0m
      [36m</div>[39m
    [36m</div>[39m
  [36m</div>[39m
  [36m<div>[39m
    [36m<div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"ready"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"routeReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"loadersReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"storeReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"keys"[39m
      [36m>[39m
        [0mfiltersData,auth[0m
      [36m</div>[39m
    [36m</div>[39m
  [36m</div>[39m
[36m</body>[39m
 ❯ Object.getElementError ../node_modules/@testing-library/react/node_modules/@testing-library/dom/dist/config.js:37:19
 ❯ getElementError ../node_modules/@testing-library/react/node_modules/@testing-library/dom/dist/query-helpers.js:20:35
 ❯ getMultipleElementsFoundError ../node_modules/@testing-library/react/node_modules/@testing-library/dom/dist/query-helpers.js:23:10
 ❯ ../node_modules/@testing-library/react/node_modules/@testing-library/dom/dist/query-helpers.js:55:13
 ❯ ../node_modules/@testing-library/react/node_modules/@testing-library/dom/dist/query-helpers.js:95:19
 ❯ src/lib/Engine/hooks/__tests__/useDataReadiness.test.tsx:61:19
     59|     // Re-render
     60|     render(<Probe options={options} />);
     61|     expect(screen.getByTestId('storeReady').textContent).toBe('true');
       |                   ^
     62|     expect(screen.getByTestId('ready').textContent).toBe('true');
     63|   });

⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/2]⎯

 FAIL  |@4-sure/ui-platform| src/lib/Engine/hooks/__tests__/useDataReadiness.test.tsx > useDataReadiness > respects asyncLoading flag from useAsyncLoaderStore
TestingLibraryElementError: Found multiple elements by: [data-testid="loadersReady"]

Here are the matching elements:

Ignored nodes: comments, script, style
[36m<div[39m
  [33mdata-testid[39m=[32m"loadersReady"[39m
[36m>[39m
  [0mtrue[0m
[36m</div>[39m

Ignored nodes: comments, script, style
[36m<div[39m
  [33mdata-testid[39m=[32m"loadersReady"[39m
[36m>[39m
  [0mtrue[0m
[36m</div>[39m

(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).

Ignored nodes: comments, script, style
[36m<body>[39m
  [36m<div>[39m
    [36m<div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"ready"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"routeReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"loadersReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"storeReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"keys"[39m
      [36m/>[39m
    [36m</div>[39m
  [36m</div>[39m
  [36m<div>[39m
    [36m<div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"ready"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"routeReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"loadersReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"storeReady"[39m
      [36m>[39m
        [0mtrue[0m
      [36m</div>[39m
      [36m<div[39m
        [33mdata-testid[39m=[32m"keys"[39m
      [36m/>[39m
    [36m</div>[39m
  [36m</div>[39m
[36m</body>[39m
 ❯ Object.getElementError ../node_modules/@testing-library/react/node_modules/@testing-library/dom/dist/config.js:37:19
 ❯ getElementError ../node_modules/@testing-library/react/node_modules/@testing-library/dom/dist/query-helpers.js:20:35
 ❯ getMultipleElementsFoundError ../node_modules/@testing-library/react/node_modules/@testing-library/dom/dist/query-helpers.js:23:10
 ❯ ../node_modules/@testing-library/react/node_modules/@testing-library/dom/dist/query-helpers.js:55:13
 ❯ ../node_modules/@testing-library/react/node_modules/@testing-library/dom/dist/query-helpers.js:95:19
 ❯ src/lib/Engine/hooks/__tests__/useDataReadiness.test.tsx:94:19
     92|     useAsyncLoaderStore.getState().setAsyncLoading(false);
     93|     render(<Probe options={options} />);
     94|     expect(screen.getByTestId('loadersReady').textContent).toBe('true'…
       |                   ^
     95|   });
     96| });

⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[2/2]⎯

error: script "test:file" exited with code 1
