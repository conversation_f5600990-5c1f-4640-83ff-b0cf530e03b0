$ vitest run src/lib/Engine/helpers/__tests__/perf.templating.test.ts
[33mThe CJS build of Vite's Node API is deprecated. See https://vitejs.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
 Vitest  "cache.dir" is deprecated, use Vite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"

 RUN  v1.6.0 /Users/<USER>/Developer/4SURE_Projects/ui-platform

stdout | src/lib/Engine/helpers/__tests__/perf.templating.test.ts > Perf: templating engines > renderTemplate basic variable substitution
[perf] renderTemplate/basic: iterations=200, totalMs=9.33, avgMs=0.0467, p95Ms=0.0640

stdout | src/lib/Engine/helpers/__tests__/perf.templating.test.ts > Perf: templating engines > renderTemplate function calls and nested evaluation
[perf] renderTemplate/functions: iterations=200, totalMs=14.23, avgMs=0.0712, p95Ms=0.0662

stdout | src/lib/Engine/helpers/__tests__/perf.templating.test.ts > Perf: templating engines > renderTemplateObject large nested object
[perf] renderTemplateObject/nested: iterations=50, totalMs=14.94, avgMs=0.2989, p95Ms=0.3925

stdout | src/lib/Engine/helpers/__tests__/perf.templating.test.ts > Perf: templating engines > renderTemplateWithJS evaluation
[perf] renderTemplateWithJS/basic: iterations=200, totalMs=23.01, avgMs=0.1151, p95Ms=0.1301

stdout | src/lib/Engine/helpers/__tests__/perf.templating.test.ts > Perf: templating engines > Memory snapshot (best-effort)
[perf] memory: heap=58.97MB, rss=375.36MB

 ✓ |@4-sure/ui-platform| src/lib/Engine/helpers/__tests__/perf.templating.test.ts  (5 tests) 66ms

 Test Files  1 passed (1)
      Tests  5 passed (5)
   Start at  12:05:22
   Duration  936ms (transform 130ms, setup 0ms, collect 263ms, tests 66ms, environment 358ms, prepare 65ms)

