$ vitest run src/lib/Engine/helpers/__tests__/render-template.integration.test.ts
[33mThe CJS build of Vite's Node API is deprecated. See https://vitejs.dev/guide/troubleshooting.html#vite-cjs-node-api-deprecated for more details.[39m
 Vitest  "cache.dir" is deprecated, use Vite's "cacheDir" instead if you want to change the cache director. Note caches will be written to "cacheDir/vitest"
⎯⎯⎯⎯⎯⎯⎯ Failed Tests 2 ⎯⎯⎯⎯⎯⎯⎯

 FAIL  |@4-sure/ui-platform| src/lib/Engine/helpers/__tests__/render-template.integration.test.ts > Template Function Registry integration across contexts > renderTemplate: resolves namespaced registry functions via flat and nested access
AssertionError: expected '{(myApp.calc)(order.amount, 0.5)}' to be 50 // Object.is equality

- Expected: 
50

+ Received: 
"{(myApp.calc)(order.amount, 0.5)}"

 ❯ src/lib/Engine/helpers/__tests__/render-template.integration.test.ts:40:44
     38|     // renderTemplate first tries global functions; to exercise nested…
     39|     const t2 = '{(myApp.calc)(order.amount, 0.5)}';
     40|     expect(renderTemplate(t2, store, fns)).toBe(50);
       |                                            ^
     41|   });
     42| 

⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[1/2]⎯

 FAIL  |@4-sure/ui-platform| src/lib/Engine/helpers/__tests__/render-template.integration.test.ts > Template Function Registry integration across contexts > renderTemplateWithJS: exposes registry functions in contextExtensions
AssertionError: expected 'js:15' to be 15 // Object.is equality

- Expected: 
15

+ Received: 
"js:15"

 ❯ src/lib/Engine/helpers/__tests__/render-template.integration.test.ts:76:20
     74|       returnRawValues: true,
     75|     });
     76|     expect(result).toBe(15);
       |                    ^
     77|   });
     78| 

⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯[2/2]⎯

error: script "test:file" exited with code 1
